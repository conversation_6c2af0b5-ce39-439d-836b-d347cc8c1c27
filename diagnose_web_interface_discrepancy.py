#!/usr/bin/env python3
"""
Diagnose Web Interface vs API Discrepancy
Compare responses between direct API calls and what web interface should receive
"""

import requests
import json
import time
from datetime import datetime

def test_api_parity():
    """Test API responses with both terminal-style and web-style requests"""
    base_url = "http://localhost:8080"
    
    print("[SEARCH] DIAGNOSING WEB INTERFACE vs API DISCREPANCY")
    print("=" * 60)
    
    test_messages = [
        {
            "message": "Hello",
            "description": "Simple greeting - should trigger conversational persona"
        },
        {
            "message": "What can you help me with?",
            "description": "Capabilities question - should be conversational"
        },
        {
            "message": "Analyze AAPL for trading",
            "description": "Trading analysis - should trigger 6-point guru format"
        }
    ]
    
    for i, test in enumerate(test_messages, 1):
        print(f"\n{i}. Testing: {test['message']}")
        print(f"   Expected: {test['description']}")
        print("-" * 50)
        
        # Test 1: Terminal-style API call (what we know works)
        print("[PHONE] Terminal-style API call:")
        try:
            terminal_response = requests.post(
                f"{base_url}/api/v1/chat",
                json={
                    "message": test["message"],
                    "session_id": f"terminal-test-{i}"
                },
                timeout=20
            )
            
            if terminal_response.status_code == 200:
                terminal_data = terminal_response.json()
                print(f"   [OK] Status: {terminal_response.status_code}")
                print(f"   [OK] Type: {terminal_data.get('type', 'unknown')}")
                print(f"   [OK] Response length: {len(terminal_data.get('response', ''))}")
                print(f"   [OK] Has confidence: {'confidence' in terminal_data}")
                print(f"   [NOTE] Sample: {terminal_data.get('response', '')[:150]}...")
                
                # Check for 6-point format in trading requests
                if "analyze" in test["message"].lower():
                    has_6_point = any(term in terminal_data.get('response', '').lower() 
                                    for term in ['why this trade', 'win/loss', 'confidence'])
                    print(f"   [OK] Has 6-point format: {has_6_point}")
                
            else:
                print(f"   [ERROR] Status: {terminal_response.status_code}")
                print(f"   [ERROR] Error: {terminal_response.text}")
                
        except Exception as e:
            print(f"   [ERROR] Terminal API Error: {e}")
        
        # Test 2: Web-style API call (matching web interface exactly)
        print("\n[WEB] Web-style API call (matching HTML interface):")
        try:
            web_response = requests.post(
                f"{base_url}/api/v1/chat",
                json={
                    "message": test["message"],
                    "session_id": f"web-test-{i}",
                    "user_id": "web_user",
                    "context": {
                        "interface": "web",
                        "timestamp": datetime.now().isoformat()
                    }
                },
                timeout=20
            )
            
            if web_response.status_code == 200:
                web_data = web_response.json()
                print(f"   [OK] Status: {web_response.status_code}")
                print(f"   [OK] Type: {web_data.get('type', 'unknown')}")
                print(f"   [OK] Response length: {len(web_data.get('response', ''))}")
                print(f"   [OK] Has confidence: {'confidence' in web_data}")
                print(f"   [NOTE] Sample: {web_data.get('response', '')[:150]}...")
                
                # Check for 6-point format in trading requests
                if "analyze" in test["message"].lower():
                    has_6_point = web = any(term in web_data.get('response', '').lower() 
                                          for term in ['why this trade', 'win/loss', 'confidence'])
                    print(f"   [OK] Has 6-point format: {has_6_point}")
                
            else:
                print(f"   [ERROR] Status: {web_response.status_code}")
                print(f"   [ERROR] Error: {web_response.text}")
                
        except Exception as e:
            print(f"   [ERROR] Web API Error: {e}")
        
        # Test 3: Compare responses
        print("\n🔄 Comparison:")
        try:
            if terminal_response.status_code == 200 and web_response.status_code == 200:
                terminal_data = terminal_response.json()
                web_data = web_response.json()
                
                # Compare key fields
                same_type = terminal_data.get('type') == web_data.get('type')
                same_length = abs(len(terminal_data.get('response', '')) - len(web_data.get('response', ''))) < 50
                same_confidence = abs(terminal_data.get('confidence', 0) - web_data.get('confidence', 0)) < 0.1
                
                print(f"   Type match: {'[OK]' if same_type else '[ERROR]'} (Terminal: {terminal_data.get('type')}, Web: {web_data.get('type')})")
                print(f"   Length similar: {'[OK]' if same_length else '[ERROR]'} (Terminal: {len(terminal_data.get('response', ''))}, Web: {len(web_data.get('response', ''))})")
                print(f"   Confidence similar: {'[OK]' if same_confidence else '[ERROR]'} (Terminal: {terminal_data.get('confidence', 0):.2f}, Web: {web_data.get('confidence', 0):.2f})")
                
                if same_type and same_length and same_confidence:
                    print("   [SUCCESS] RESPONSES ARE EQUIVALENT!")
                else:
                    print("   [WARN] RESPONSES DIFFER - INVESTIGATING...")
                    
                    # Show detailed comparison for different responses
                    if not same_type:
                        print(f"      Type difference: '{terminal_data.get('type')}' vs '{web_data.get('type')}'")
                    
                    if not same_length:
                        print(f"      Length difference: {len(terminal_data.get('response', ''))} vs {len(web_data.get('response', ''))}")
                        print(f"      Terminal sample: {terminal_data.get('response', '')[:100]}...")
                        print(f"      Web sample: {web_data.get('response', '')[:100]}...")
            else:
                print("   [ERROR] Cannot compare - one or both requests failed")
                
        except Exception as e:
            print(f"   [ERROR] Comparison error: {e}")
        
        print("\n" + "=" * 60)
        time.sleep(1)  # Brief pause between tests

def test_web_interface_javascript_simulation():
    """Simulate exactly what the web interface JavaScript does"""
    print("\n[DESKTOP] SIMULATING WEB INTERFACE JAVASCRIPT")
    print("=" * 60)
    
    base_url = "http://localhost:8080"
    
    # Simulate the exact JavaScript fetch call
    test_message = "Hello from simulated web interface"
    session_id = f"session_{int(time.time() * 1000)}_{int(time.time()) % 1000}"
    
    print(f"Message: {test_message}")
    print(f"Session ID: {session_id}")
    
    try:
        # This exactly matches the JavaScript fetch call
        response = requests.post(
            f"{base_url}/api/v1/chat",
            headers={'Content-Type': 'application/json'},
            json={
                "message": test_message,
                "session_id": session_id,
                "user_id": "web_user",
                "context": {
                    "interface": "web",
                    "timestamp": datetime.now().isoformat()
                }
            },
            timeout=15
        )
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"\nResponse Data:")
            print(f"  - response: {data.get('response', 'MISSING')[:200]}...")
            print(f"  - type: {data.get('type', 'MISSING')}")
            print(f"  - confidence: {data.get('confidence', 'MISSING')}")
            print(f"  - context: {data.get('context', 'MISSING')}")
            
            # Simulate the JavaScript response processing
            processed_response = {
                "response": data.get('response') or data.get('message') or 'I received your message.',
                "type": data.get('type') or 'general',
                "confidence": data.get('confidence') or 0.8
            }
            
            print(f"\nProcessed by JavaScript (as web interface would see it):")
            print(f"  - response: {processed_response['response'][:200]}...")
            print(f"  - type: {processed_response['type']}")
            print(f"  - confidence: {processed_response['confidence']}")
            
        else:
            print(f"Error Response: {response.text}")
            
            # Simulate JavaScript fallback
            fallback_response = {
                "response": "I'm A.T.L.A.S. powered by Predicto. I can help with stock analysis, trading strategies, and market insights. What would you like to know?",
                "type": "fallback",
                "confidence": 0.7
            }
            
            print(f"\nJavaScript Fallback Response:")
            print(f"  - response: {fallback_response['response']}")
            print(f"  - type: {fallback_response['type']}")
            print(f"  - confidence: {fallback_response['confidence']}")
            
    except Exception as e:
        print(f"Simulation Error: {e}")
        
        # Simulate JavaScript error handling
        error_response = {
            "response": "I'm A.T.L.A.S. powered by Predicto. I can help with stock analysis, trading strategies, and market insights. What would you like to know?",
            "type": "fallback",
            "confidence": 0.7
        }
        
        print(f"\nJavaScript Error Fallback:")
        print(f"  - response: {error_response['response']}")
        print(f"  - type: {error_response['type']}")
        print(f"  - confidence: {error_response['confidence']}")

def check_server_logs_for_web_requests():
    """Instructions for checking server logs"""
    print("\n📋 NEXT STEPS FOR DIAGNOSIS")
    print("=" * 60)
    print("1. Check the server logs for web interface requests")
    print("2. Look for any differences in how web requests are processed")
    print("3. Verify that web requests trigger the same persona detection")
    print("4. Confirm OpenAI API calls are made for web requests")
    print("5. Check if conversation history is being maintained for web sessions")
    
    print("\n[SEARCH] What to look for in server logs:")
    print("- 'Predicto processing: [message]...' entries")
    print("- 'Detected conversational message' or 'Detected trading request'")
    print("- 'Using Stock Market Guru mode' for trading requests")
    print("- 'HTTP Request: POST https://api.openai.com/v1/chat/completions'")
    print("- 'POST /api/v1/chat HTTP/1.1 200' access logs")

if __name__ == "__main__":
    test_api_parity()
    test_web_interface_javascript_simulation()
    check_server_logs_for_web_requests()
