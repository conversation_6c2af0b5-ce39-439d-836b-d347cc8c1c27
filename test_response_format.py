#!/usr/bin/env python3
"""
Test to capture and analyze the actual response format from A.T.L.A.S.
"""
import requests
import json
import time

def test_response_format():
    """Test the actual response format being returned"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_message = "Analyze AAPL for a potential trade"
    
    payload = {
        "message": test_message,
        "session_id": "format_test_123",
        "user_id": "test_user",
        "context": {
            "interface": "test",
            "timestamp": "2025-06-30T16:40:00Z"
        }
    }
    
    print("[SEARCH] Testing A.T.L.A.S. Response Format")
    print("=" * 50)
    print(f"Message: {test_message}")
    print(f"URL: {url}")
    print()
    
    try:
        print("📤 Sending request...")
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"[DATA] Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("[OK] Response received successfully!")
            print()
            
            # Analyze response structure
            print("📋 Response Structure:")
            print(f"  Type: {data.get('type', 'N/A')}")
            print(f"  Confidence: {data.get('confidence', 'N/A')}")
            
            response_text = data.get('response', '')
            print(f"  Length: {len(response_text)} characters")
            
            # Check for format indicators
            print("\n[SEARCH] Format Analysis:")
            
            # Check for sophisticated 6-point format
            sophisticated_markers = [
                "**A.T.L.A.S powered by Predicto - Stock Market God**",
                "**1. Why This Trade?**",
                "**2. Win/Loss Probabilities**",
                "**3. Potential Money In or Out**",
                "**4. Smart Stop Plans**",
                "**5. Market Context**",
                "**6. Confidence Score**"
            ]
            
            found_sophisticated = sum(1 for marker in sophisticated_markers if marker in response_text)
            print(f"  Sophisticated 6-point markers: {found_sophisticated}/7")
            
            # Check for Trading God format
            trading_god_markers = [
                "| ACTION:",
                "| CONFIDENCE:",
                "Entry:",
                "Target:",
                "Stop:",
                "Strategy:"
            ]
            
            found_trading_god = sum(1 for marker in trading_god_markers if marker in response_text)
            print(f"  Trading God format markers: {found_trading_god}/6")
            
            # Check for hardcoded responses
            hardcoded_markers = [
                "I'm A.T.L.A.S. powered by Predicto",
                "Advanced Trading & Learning Analysis System",
                "What would you like to explore today?"
            ]
            
            found_hardcoded = sum(1 for marker in hardcoded_markers if marker in response_text)
            print(f"  Hardcoded response markers: {found_hardcoded}/3")
            
            # Determine response type
            print("\n[TARGET] Response Type Analysis:")
            if found_sophisticated >= 4:
                print("  [OK] SOPHISTICATED 6-POINT FORMAT detected")
                response_type = "sophisticated"
            elif found_trading_god >= 3:
                print("  [WARN]  TRADING GOD FORMAT detected")
                response_type = "trading_god"
            elif found_hardcoded >= 1:
                print("  [ERROR] HARDCODED RESPONSE detected")
                response_type = "hardcoded"
            else:
                print("  ❓ UNKNOWN FORMAT detected")
                response_type = "unknown"
            
            # Show context if available
            if 'context' in data:
                print(f"\n[TOOL] Response Context:")
                context = data['context']
                for key, value in context.items():
                    if isinstance(value, (str, int, float, bool)):
                        print(f"  {key}: {value}")
            
            # Show response preview
            print(f"\n💬 Response Preview (first 500 chars):")
            print("-" * 50)
            preview = response_text[:500] + "..." if len(response_text) > 500 else response_text
            print(preview)
            print("-" * 50)
            
            # Save full response to file for analysis
            with open("last_response.txt", "w", encoding="utf-8") as f:
                f.write(f"Response Type: {response_type}\n")
                f.write(f"Status Code: {response.status_code}\n")
                f.write(f"Response Data: {json.dumps(data, indent=2)}\n")
                f.write(f"\nFull Response Text:\n{response_text}")
            
            print(f"\n[FLOPPY] Full response saved to 'last_response.txt'")
            
            return response_type
            
        else:
            print(f"[ERROR] HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return "error"
            
    except Exception as e:
        print(f"[ERROR] Error: {e}")
        return "error"

if __name__ == "__main__":
    result = test_response_format()
    print(f"\n[FINISH] Test completed. Result: {result}")
