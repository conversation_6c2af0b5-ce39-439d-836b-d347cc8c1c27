#!/usr/bin/env python3
"""
Quick test to verify the guru response format fix
"""
import requests
import json

def test_response():
    """Test if the sophisticated guru response is being returned"""
    
    url = "http://localhost:8080/api/v1/chat"
    payload = {
        "message": "hello",
        "session_id": "test123"
    }
    
    try:
        print("🧪 Testing A.T.L.A.S. Guru Response Format...")
        
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get("response", "")
            
            # Check for sophisticated format markers
            sophisticated_markers = [
                "**A.T.L.A.S powered by Predicto - Stock Market God**",
                "**1. Why This Trade?**",
                "**2. Win/Loss Probabilities**",
                "**3. Potential Money In or Out**",
                "**4. Smart Stop Plans**",
                "**5. Market Context**",
                "**6. Confidence Score**"
            ]
            
            found_markers = [marker for marker in sophisticated_markers if marker in response_text]
            
            # Check for generic format markers
            generic_markers = ["SPY: $", "| ACTION:", "| CONFIDENCE:"]
            found_generic = [marker for marker in generic_markers if marker in response_text]
            
            print(f"[DATA] Status: {response.status_code}")
            print(f"📏 Response length: {len(response_text)} characters")
            print(f"✨ Sophisticated markers found: {len(found_markers)}/7")
            print(f"[TOOL] Generic markers found: {len(found_generic)}/3")
            
            if len(found_markers) >= 4:
                print("[OK] SUCCESS: Sophisticated 6-point format detected!")
                print("[OK] Fix is working - Trading God transformation was bypassed")
                return True
            elif len(found_generic) >= 2:
                print("[ERROR] ISSUE: Generic format detected")
                print("[ERROR] Trading God transformation is still overriding the response")
                return False
            else:
                print("❓ UNKNOWN: Response format unclear")
                print(f"First 200 chars: {response_text[:200]}")
                return False
            
        else:
            print(f"[ERROR] Request failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Error: {e}")
        return False

if __name__ == "__main__":
    success = test_response()
    exit(0 if success else 1)
