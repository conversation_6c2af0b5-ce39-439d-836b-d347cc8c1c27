#!/usr/bin/env python3
"""
Test the final conversational intelligence fix
"""
import requests
import json

def test_final_fix():
    url = "http://localhost:8080/api/v1/chat"
    
    # Test conversational message
    payload = {
        "message": "hello",
        "session_id": "final_fix_test"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            response_type = data.get('type', 'unknown')
            response_text = data.get('response', '')
            
            print(f"Message: 'hello'")
            print(f"Response Type: {response_type}")
            print(f"Response Length: {len(response_text)} chars")
            
            # Check if it's a proper greeting
            if response_type == "greeting" and "Hello!" in response_text:
                print("[OK] SUCCESS: Proper greeting response!")
                print(f"Preview: {response_text[:100]}...")
                return True
            else:
                print("[ERROR] FAIL: Still getting wrong response type")
                print(f"Preview: {response_text[:100]}...")
                return False
        else:
            print(f"Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    success = test_final_fix()
    exit(0 if success else 1)
