#!/usr/bin/env python3
"""
A.T.L.A.S. Comprehensive System Test Suite
Tests all 25+ features with specific validation criteria
"""

import asyncio
import json
import time
import requests
from typing import Dict, List, Any
from datetime import datetime
import sys
import os

class ATLASSystemTester:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.session_id = f"test-{int(time.time())}"
        self.test_results = []
        self.passed_tests = 0
        self.total_tests = 0
        
    def log_test(self, test_name: str, passed: bool, details: str = "", response_time: float = 0):
        """Log test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "[OK] PASS"
        else:
            status = "[ERROR] FAIL"
            
        result = {
            "test_name": test_name,
            "status": status,
            "passed": passed,
            "details": details,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        print(f"{status} | {test_name} | {response_time:.2f}s | {details}")
        
    def test_health_endpoint(self):
        """Test system health and initialization status"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/api/v1/health", timeout=10)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                engines_active = sum(1 for status in data.get('engines', {}).values() if status == 'active')
                total_engines = len(data.get('engines', {}))
                
                self.log_test(
                    "Health Endpoint", 
                    True, 
                    f"Status: {data.get('status', 'unknown')}, Engines: {engines_active}/{total_engines}",
                    response_time
                )
                return data
            else:
                self.log_test("Health Endpoint", False, f"HTTP {response.status_code}", response_time)
                return None
        except Exception as e:
            self.log_test("Health Endpoint", False, f"Error: {str(e)}")
            return None
    
    def test_chat_interface(self):
        """Test main conversational AI interface"""
        test_messages = [
            "Hello, I'm new to trading",
            "Analyze AAPL for a potential trade",
            "What's the best options strategy for TSLA?",
            "I want to make $100 today",
            "Scan for TTM Squeeze signals"
        ]
        
        for message in test_messages:
            try:
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/api/v1/chat",
                    json={"message": message, "session_id": self.session_id},
                    timeout=30
                )
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get('response', '')
                    
                    # Check for 6-point format in trading responses
                    has_6_point_format = self._check_6_point_format(response_text)
                    has_atlas_branding = "A.T.L.A.S" in response_text and "Predicto" in response_text
                    
                    details = f"Length: {len(response_text)}, 6-Point: {has_6_point_format}, Branding: {has_atlas_branding}"
                    self.log_test(f"Chat: {message[:30]}...", True, details, response_time)
                else:
                    self.log_test(f"Chat: {message[:30]}...", False, f"HTTP {response.status_code}", response_time)
                    
            except Exception as e:
                self.log_test(f"Chat: {message[:30]}...", False, f"Error: {str(e)}")
    
    def test_market_data_endpoints(self):
        """Test market data and analysis endpoints"""
        symbols = ["AAPL", "TSLA", "MSFT"]
        
        for symbol in symbols:
            # Test quote endpoint
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}/api/v1/quote/{symbol}", timeout=10)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    has_price = 'price' in data or 'current_price' in data
                    self.log_test(f"Quote: {symbol}", has_price, f"Data keys: {list(data.keys())}", response_time)
                else:
                    self.log_test(f"Quote: {symbol}", False, f"HTTP {response.status_code}", response_time)
            except Exception as e:
                self.log_test(f"Quote: {symbol}", False, f"Error: {str(e)}")
    
    def test_ttm_squeeze_scanner(self):
        """Test TTM Squeeze pattern detection"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/api/v1/scan", timeout=20)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                signals = data.get('signals', [])
                self.log_test("TTM Squeeze Scanner", True, f"Found {len(signals)} signals", response_time)
            else:
                self.log_test("TTM Squeeze Scanner", False, f"HTTP {response.status_code}", response_time)
        except Exception as e:
            self.log_test("TTM Squeeze Scanner", False, f"Error: {str(e)}")
    
    def test_portfolio_endpoints(self):
        """Test portfolio management endpoints"""
        endpoints = [
            "/api/v1/portfolio",
            "/api/v1/portfolio/risk-analysis",
            "/api/v1/portfolio/optimization"
        ]
        
        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", timeout=15)
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    self.log_test(f"Portfolio: {endpoint.split('/')[-1]}", True, f"Keys: {list(data.keys())}", response_time)
                else:
                    self.log_test(f"Portfolio: {endpoint.split('/')[-1]}", False, f"HTTP {response.status_code}", response_time)
            except Exception as e:
                self.log_test(f"Portfolio: {endpoint.split('/')[-1]}", False, f"Error: {str(e)}")
    
    def test_options_capabilities(self):
        """Test options trading capabilities"""
        # Test options analysis
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/v1/chat",
                json={"message": "Analyze AAPL options for next week", "session_id": self.session_id},
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                has_options_content = any(term in response_text.lower() for term in ['option', 'call', 'put', 'strike', 'expiration'])
                self.log_test("Options Analysis", has_options_content, f"Length: {len(response_text)}", response_time)
            else:
                self.log_test("Options Analysis", False, f"HTTP {response.status_code}", response_time)
        except Exception as e:
            self.log_test("Options Analysis", False, f"Error: {str(e)}")
    
    def _check_6_point_format(self, response_text: str) -> bool:
        """Check if response follows 6-point Stock Market God format"""
        required_sections = [
            "why this trade",
            "win/loss prob",
            "money in",
            "stop plan",
            "market context",
            "confidence"
        ]
        
        response_lower = response_text.lower()
        found_sections = sum(1 for section in required_sections if section in response_lower)
        return found_sections >= 4  # At least 4 out of 6 sections
    
    def run_comprehensive_test(self):
        """Run all tests and generate report"""
        print("[LAUNCH] Starting A.T.L.A.S. Comprehensive System Test")
        print("=" * 60)
        
        # Test system health first
        health_data = self.test_health_endpoint()
        
        if not health_data:
            print("[ERROR] System health check failed - aborting tests")
            return
        
        # Run all test categories
        print("\n[DATA] Testing Chat Interface...")
        self.test_chat_interface()
        
        print("\n[UP] Testing Market Data...")
        self.test_market_data_endpoints()
        
        print("\n[SEARCH] Testing TTM Squeeze Scanner...")
        self.test_ttm_squeeze_scanner()
        
        print("\n[TRADE] Testing Portfolio Management...")
        self.test_portfolio_endpoints()
        
        print("\n[TARGET] Testing Options Capabilities...")
        self.test_options_capabilities()
        
        # Generate final report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📋 A.T.L.A.S. SYSTEM TEST REPORT")
        print("=" * 60)
        
        pass_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        print(f"[DATA] Overall Results:")
        print(f"   • Total Tests: {self.total_tests}")
        print(f"   • Passed: {self.passed_tests}")
        print(f"   • Failed: {self.total_tests - self.passed_tests}")
        print(f"   • Pass Rate: {pass_rate:.1f}%")
        
        if pass_rate >= 90:
            print("[SUCCESS] EXCELLENT - System is production ready!")
        elif pass_rate >= 75:
            print("[OK] GOOD - System is mostly functional with minor issues")
        elif pass_rate >= 50:
            print("[WARN]  NEEDS WORK - System has significant issues")
        else:
            print("[ERROR] CRITICAL - System requires major fixes")
        
        # Save detailed results
        with open('atlas_test_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\n[DOC] Detailed results saved to: atlas_test_results.json")

if __name__ == "__main__":
    tester = ATLASSystemTester()
    tester.run_comprehensive_test()
