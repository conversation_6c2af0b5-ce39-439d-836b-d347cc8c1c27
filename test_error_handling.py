#!/usr/bin/env python3
"""
Test Enhanced Error Handling System
Validates that the error handling improvements work correctly
"""

import asyncio
import sys
import os

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

async def test_error_handler():
    """Test the enhanced error handler"""
    print("Testing Enhanced Error Handler...")
    print("=" * 50)
    
    try:
        from atlas_error_handler import (
            get_error_handler, atlas_error_handler, ErrorSeverity,
            setup_component_recovery_strategies, safe_api_call
        )
        
        # Initialize error handler
        handler = get_error_handler()
        setup_component_recovery_strategies()
        
        print("[OK] Error handler imported and initialized successfully")
        
        # Test 1: Basic error handling
        print("\nTest 1: Basic error handling")
        try:
            raise ValueError("Test error for validation")
        except Exception as e:
            error_context = await handler.handle_error(
                component="test_component",
                operation="test_operation",
                error=e,
                severity=ErrorSeverity.MEDIUM
            )
            print(f"[OK] Error handled: {error_context.error_message}")
        
        # Test 2: Fallback response
        print("\nTest 2: Fallback response")
        fallback = handler.get_fallback_response("chat_response")
        print(f"[OK] Fallback response: {fallback['response'][:50]}...")
        
        # Test 3: Component status tracking
        print("\nTest 3: Component status tracking")
        status = handler.get_component_status("test_component")
        print(f"[OK] Component status: {status}")
        
        # Test 4: Error summary
        print("\nTest 4: Error summary")
        summary = handler.get_error_summary()
        print(f"[OK] Error summary - Total components: {summary['total_components']}")
        print(f"[OK] Failed components: {summary['failed_components']}")
        print(f"[OK] Degraded components: {summary['degraded_components']}")
        
        # Test 5: Decorator usage
        print("\nTest 5: Decorator usage")
        
        @atlas_error_handler(
            component="test_decorator",
            operation="test_function",
            severity=ErrorSeverity.LOW
        )
        async def test_function_with_error():
            raise RuntimeError("Decorator test error")
        
        result = await test_function_with_error()
        print(f"[OK] Decorator handled error, returned: {type(result)}")
        
        # Test 6: Safe API call
        print("\nTest 6: Safe API call")
        
        async def failing_api_call():
            raise ConnectionError("API connection failed")
        
        result = await safe_api_call(
            failing_api_call,
            fallback={"status": "fallback", "data": None}
        )
        print(f"[OK] Safe API call returned fallback: {result}")
        
        print("\n" + "=" * 50)
        print("[SUCCESS] All error handling tests passed!")
        return True
        
    except ImportError as e:
        print(f"[ERROR] Could not import error handler: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        return False

async def test_system_integration():
    """Test error handling integration with the main system"""
    print("\nTesting System Integration...")
    print("=" * 50)
    
    try:
        # Test that the system can start with error handling
        sys.path.append(os.path.join(os.path.dirname(__file__), '1_main_chat_engine'))
        
        # Import orchestrator to test integration
        from atlas_orchestrator import AtlasOrchestrator
        
        # Create orchestrator instance
        orchestrator = AtlasOrchestrator()
        print("[OK] Orchestrator created with error handling integration")
        
        # Check if error handler is available
        if hasattr(orchestrator, '_error_handler') and orchestrator._error_handler:
            print("[OK] Error handler is integrated into orchestrator")
        else:
            print("[INFO] Error handler not integrated (may be expected)")
        
        # Test message processing with potential errors
        print("\nTesting message processing...")
        
        # This should work even if components aren't fully initialized
        response = await orchestrator.process_message("Hello, test message")
        print(f"[OK] Message processed successfully: {response.type}")
        
        print("\n" + "=" * 50)
        print("[SUCCESS] System integration test passed!")
        return True
        
    except Exception as e:
        print(f"[ERROR] System integration test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("A.T.L.A.S. Enhanced Error Handling Test Suite")
    print("=" * 60)
    
    # Test 1: Error handler functionality
    test1_passed = await test_error_handler()
    
    # Test 2: System integration
    test2_passed = await test_system_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Error Handler Tests: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"System Integration: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n[SUCCESS] All tests passed! Error handling is working correctly.")
        return 0
    else:
        print("\n[FAILURE] Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
