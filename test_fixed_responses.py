#!/usr/bin/env python3
"""
Test the fixed conversational responses
"""
import requests
import json
import time

def test_fixed_responses():
    """Test the fixed response system"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_cases = [
        {
            "name": "Casual Greeting",
            "message": "whats up?",
            "expected_type": "greeting"
        },
        {
            "name": "Formal Greeting", 
            "message": "hello",
            "expected_type": "greeting"
        },
        {
            "name": "Capability Question",
            "message": "What can you do?",
            "expected_type": "capabilities"
        },
        {
            "name": "Stock Analysis",
            "message": "analyze AAPL",
            "expected_type": "guru_trade_plan"
        }
    ]
    
    print("🧪 Testing Fixed A.T.L.A.S. Responses")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[NOTE] Test {i}: {test_case['name']}")
        print(f"Message: '{test_case['message']}'")
        print("-" * 40)
        
        payload = {
            "message": test_case["message"],
            "session_id": f"fixed_test_{i}",
            "user_id": "test_user"
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, json=payload, timeout=15)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                response_type = data.get('type', 'unknown')
                confidence = data.get('confidence', 0.0)
                
                print(f"[OK] Status: {response.status_code}")
                print(f"[DATA] Type: {response_type}")
                print(f"[TARGET] Expected: {test_case['expected_type']}")
                print(f"[OK] Match: {'YES' if response_type == test_case['expected_type'] else 'NO'}")
                print(f"📏 Length: {len(response_text)} chars")
                print(f"⏱️  Time: {end_time - start_time:.2f}s")
                
                # Show response preview
                preview_length = 150
                preview = response_text[:preview_length] + "..." if len(response_text) > preview_length else response_text
                print(f"\n💬 Response Preview:")
                print(f"'{preview}'")
                
                # Check for natural vs templated content
                if test_case['name'] in ['Casual Greeting', 'Capability Question']:
                    if len(response_text) < 300 and not any(marker in response_text for marker in ["[DATA]", "[SEARCH]", "[TRADE]", "[TARGET]"]):
                        print(f"[OK] Response appears natural and concise")
                    else:
                        print(f"[WARN]  Response may still be templated")
                        
            else:
                print(f"[ERROR] HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"[ERROR] Error: {e}")
        
        print("\n" + "="*50)
        time.sleep(2)

if __name__ == "__main__":
    test_fixed_responses()
