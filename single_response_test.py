#!/usr/bin/env python3
"""
Single response test to see the actual response format
"""
import requests
import json

def test_single_response():
    """Test a single question and show the full response"""
    
    url = "http://localhost:8080/api/v1/chat"
    payload = {
        "message": "analyze TSLA stock",
        "session_id": "single_test"
    }
    
    try:
        print("🧪 Testing Single A.T.L.A.S. Response...")
        print(f"[NOTE] Question: '{payload['message']}'")
        print()
        
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get("response", "")
            
            print(f"[DATA] Status: {response.status_code}")
            print(f"📏 Response Length: {len(response_text)} characters")
            print()
            
            # Check for sophisticated format markers
            sophisticated_markers = [
                "**A.T.L.A.S powered by Predicto - Stock Market God**",
                "**1. Why This Trade?**",
                "**2. Win/Loss Probabilities**",
                "**3. Potential Money In or Out**",
                "**4. Smart Stop Plans**",
                "**5. Market Context**",
                "**6. Confidence Score**"
            ]
            
            found_markers = [marker for marker in sophisticated_markers if marker in response_text]
            
            # Check for generic format markers
            generic_markers = ["SPY: $", "| ACTION:", "| CONFIDENCE:"]
            found_generic = [marker for marker in generic_markers if marker in response_text]
            
            print("[SEARCH] Format Analysis:")
            print(f"   ✨ Sophisticated markers found: {len(found_markers)}/7")
            for marker in found_markers:
                print(f"      [OK] {marker}")
            
            print(f"   [TOOL] Generic markers found: {len(found_generic)}/3")
            for marker in found_generic:
                print(f"      [ERROR] {marker}")
            
            print()
            
            if len(found_markers) >= 4:
                print("[SUCCESS] SUCCESS: Sophisticated 6-point format detected!")
                print("[OK] Fix is working - Trading God transformation was bypassed")
            elif len(found_generic) >= 2:
                print("[ERROR] FAILURE: Generic format detected")
                print("[ERROR] Trading God transformation is still overriding the response")
            else:
                print("❓ UNCLEAR: Response format unclear")
            
            print()
            print("[DOC] FULL RESPONSE:")
            print("=" * 80)
            print(response_text)
            print("=" * 80)
            
        else:
            print(f"[ERROR] Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"[ERROR] Error: {e}")

if __name__ == "__main__":
    test_single_response()
