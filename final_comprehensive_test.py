#!/usr/bin/env python3
"""
Final comprehensive test of A.T.L.A.S. conversational intelligence fixes
"""
import requests
import json
import time
from datetime import datetime

def final_comprehensive_test():
    """Final test to validate all conversational intelligence fixes"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_cases = [
        {
            "category": "Conversational",
            "name": "Casual Greeting",
            "message": "whats up?",
            "expected_type": "greeting",
            "expected_features": ["natural", "brief", "A.T.L.A.S. powered by Predicto"],
            "unexpected_features": ["[DATA]", "[SEARCH]", "[TRADE]", "educational mentor", "Stock Market God"]
        },
        {
            "category": "Conversational",
            "name": "Formal Greeting", 
            "message": "hello",
            "expected_type": "greeting",
            "expected_features": ["Hello", "A.T.L.A.S. powered by Predicto"],
            "unexpected_features": ["[DATA]", "[SEARCH]", "[TRADE]", "educational mentor", "Stock Market God"]
        },
        {
            "category": "Conversational",
            "name": "Capability Question",
            "message": "What can you do?",
            "expected_type": "capabilities",
            "expected_features": ["A.T.L.A.S. powered by Predicto", "trading", "analysis"],
            "unexpected_features": ["[DATA] **Core Trading Features:**", "[SEARCH] **Market Intelligence:**", "[TRADE] **Professional Tools:**"]
        },
        {
            "category": "Trading",
            "name": "AAPL Analysis",
            "message": "analyze AAPL",
            "expected_type": "guru_trade_plan",
            "expected_features": ["AAPL", "A.T.L.A.S powered by Predicto", "Entry:", "Target:", "Stop:"],
            "unexpected_features": ["NVDA", "MSFT", "SPY", "Hello!", "What can you do"]
        },
        {
            "category": "Trading",
            "name": "TSLA Analysis",
            "message": "analyze TSLA for trading",
            "expected_type": "guru_trade_plan", 
            "expected_features": ["TSLA", "A.T.L.A.S powered by Predicto", "Entry:", "Target:", "Stop:"],
            "unexpected_features": ["AAPL", "NVDA", "MSFT", "Hello!", "What can you do"]
        },
        {
            "category": "Trading",
            "name": "Goal-Based Trading",
            "message": "I want to make $200 today",
            "expected_type": "guru_trade_plan",
            "expected_features": ["A.T.L.A.S powered by Predicto", "Entry:", "Target:", "Stop:"],
            "unexpected_features": ["Hello!", "What can you do", "educational mentor"]
        }
    ]
    
    print("[TARGET] A.T.L.A.S. Conversational Intelligence - Final Comprehensive Test")
    print("=" * 80)
    print(f"Testing at: {datetime.now()}")
    print(f"Endpoint: {url}")
    print()
    
    results = []
    conversational_passed = 0
    trading_passed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"[NOTE] Test {i}: {test_case['category']} - {test_case['name']}")
        print(f"Message: '{test_case['message']}'")
        print("-" * 70)
        
        payload = {
            "message": test_case["message"],
            "session_id": f"final_comprehensive_{i}",
            "user_id": "test_user"
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, json=payload, timeout=20)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                response_type = data.get('type', 'unknown')
                confidence = data.get('confidence', 0.0)
                
                # Check response type
                type_correct = response_type == test_case['expected_type']
                
                # Check expected features
                expected_found = sum(1 for feature in test_case.get('expected_features', []) if feature in response_text)
                expected_total = len(test_case.get('expected_features', []))
                expected_score = expected_found / expected_total if expected_total > 0 else 1.0
                
                # Check unexpected features (should NOT be present)
                unexpected_found = sum(1 for feature in test_case.get('unexpected_features', []) if feature in response_text)
                unexpected_total = len(test_case.get('unexpected_features', []))
                unexpected_score = 1.0 - (unexpected_found / unexpected_total) if unexpected_total > 0 else 1.0
                
                # Overall score
                overall_score = (int(type_correct) + expected_score + unexpected_score) / 3
                
                # Determine pass/fail
                passed = overall_score >= 0.8
                status = "[OK] PASS" if passed else "[ERROR] FAIL"
                
                print(f"{status} - Score: {overall_score:.1%}")
                print(f"[DATA] Type: {response_type} (expected: {test_case['expected_type']}) {'[OK]' if type_correct else '[ERROR]'}")
                print(f"📏 Length: {len(response_text)} chars")
                print(f"⏱️  Time: {response_time:.2f}s")
                print(f"[TARGET] Confidence: {confidence}")
                print(f"[OK] Expected features: {expected_found}/{expected_total}")
                print(f"[ERROR] Unexpected features: {unexpected_found}/{unexpected_total}")
                
                # Track category performance
                if test_case['category'] == 'Conversational' and passed:
                    conversational_passed += 1
                elif test_case['category'] == 'Trading' and passed:
                    trading_passed += 1
                
                # Show response preview
                preview = response_text[:200] + "..." if len(response_text) > 200 else response_text
                print(f"\n💬 Response Preview:")
                print(f"'{preview}'")
                
                # Specific analysis
                if test_case['category'] == 'Conversational':
                    if len(response_text) < 400 and not any(marker in response_text for marker in ["[DATA]", "[SEARCH]", "[TRADE]"]):
                        print(f"[OK] Response is natural and concise")
                    else:
                        print(f"[WARN]  Response may still be templated or too long")
                elif test_case['category'] == 'Trading':
                    if "analyze AAPL" in test_case['message'] and "AAPL" in response_text:
                        print(f"[OK] Correctly uses requested symbol (AAPL)")
                    elif "analyze TSLA" in test_case['message'] and "TSLA" in response_text:
                        print(f"[OK] Correctly uses requested symbol (TSLA)")
                    elif any(wrong_symbol in response_text for wrong_symbol in ["NVDA", "MSFT", "SPY"]) and test_case['message'] not in ["I want to make $200 today"]:
                        print(f"[ERROR] Uses wrong symbol instead of requested one")
                    else:
                        print(f"[OK] Trading response appears appropriate")
                
                results.append({
                    "test": f"{test_case['category']}: {test_case['name']}",
                    "status": "PASS" if passed else "FAIL",
                    "score": overall_score,
                    "category": test_case['category'],
                    "type_correct": type_correct,
                    "expected_type": test_case['expected_type'],
                    "actual_type": response_type,
                    "response_length": len(response_text),
                    "response_time": response_time
                })
                
            else:
                print(f"[ERROR] HTTP Error: {response.status_code}")
                results.append({
                    "test": f"{test_case['category']}: {test_case['name']}",
                    "status": "ERROR",
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"[ERROR] Error: {e}")
            results.append({
                "test": f"{test_case['category']}: {test_case['name']}",
                "status": "ERROR",
                "error": str(e)
            })
        
        print("\n" + "="*80 + "\n")
        time.sleep(2)
    
    # Final Summary
    print("🏆 FINAL COMPREHENSIVE TEST SUMMARY")
    print("=" * 80)
    
    total_tests = len(test_cases)
    total_passed = sum(1 for r in results if r.get('status') == 'PASS')
    total_failed = sum(1 for r in results if r.get('status') == 'FAIL')
    total_errors = sum(1 for r in results if r.get('status') == 'ERROR')
    
    conversational_total = sum(1 for tc in test_cases if tc['category'] == 'Conversational')
    trading_total = sum(1 for tc in test_cases if tc['category'] == 'Trading')
    
    print(f"[DATA] Overall Results:")
    print(f"   Total Tests: {total_tests}")
    print(f"   [OK] Passed: {total_passed}")
    print(f"   [ERROR] Failed: {total_failed}")
    print(f"   🔥 Errors: {total_errors}")
    print(f"   [TARGET] Success Rate: {(total_passed/total_tests)*100:.1f}%")
    
    print(f"\n📋 Category Breakdown:")
    print(f"   💬 Conversational: {conversational_passed}/{conversational_total} passed ({(conversational_passed/conversational_total)*100:.1f}%)")
    print(f"   [UP] Trading: {trading_passed}/{trading_total} passed ({(trading_passed/trading_total)*100:.1f}%)")
    
    # Final verdict
    success_rate = (total_passed / total_tests) * 100
    conversational_rate = (conversational_passed / conversational_total) * 100 if conversational_total > 0 else 0
    trading_rate = (trading_passed / trading_total) * 100 if trading_total > 0 else 0
    
    print(f"\n[TARGET] FINAL VERDICT:")
    if success_rate >= 90 and conversational_rate >= 90 and trading_rate >= 90:
        print("[SUCCESS] EXCELLENT: A.T.L.A.S. conversational intelligence is working perfectly!")
        print("[OK] Natural, contextual responses for greetings and help requests")
        print("[OK] Sophisticated trading analysis with correct symbol usage")
        print("[OK] Proper persona detection and response protection")
        print("[OK] Dynamic AI-generated responses instead of static templates")
    elif success_rate >= 80:
        print("👍 VERY GOOD: System is working well with minor areas for improvement")
    elif success_rate >= 60:
        print("👌 GOOD: Most functionality working, some issues to address")
    else:
        print("[ERROR] NEEDS WORK: Significant issues remain")
    
    # Save results
    with open("final_comprehensive_test_results.json", "w") as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed": total_passed,
                "failed": total_failed,
                "errors": total_errors,
                "success_rate": success_rate,
                "conversational_rate": conversational_rate,
                "trading_rate": trading_rate
            },
            "detailed_results": results
        }, f, indent=2)
    
    print(f"\n[FLOPPY] Detailed results saved to 'final_comprehensive_test_results.json'")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = final_comprehensive_test()
    exit(0 if success else 1)
