#!/usr/bin/env python3
"""
Phase 3: Test TTM Squeeze Integration with A.T.L.A.S.
Validate enhanced TTM Squeeze scanning through conversational interface
"""

import requests
import json
import time
from datetime import datetime

def test_ttm_squeeze_conversational_integration():
    """Test TTM Squeeze functionality through conversational interface"""
    base_url = "http://localhost:8080"
    
    print("[TARGET] PHASE 3: TTM SQUEEZE INTEGRATION TEST")
    print("=" * 60)
    
    test_cases = [
        {
            "message": "Scan for TTM Squeeze signals",
            "description": "Market-wide TTM Squeeze scan",
            "expect_type": "ttm_squeeze_scan"
        },
        {
            "message": "Find TTM Squeeze opportunities in AAPL",
            "description": "Symbol-specific TTM analysis",
            "expect_type": "ttm_squeeze_scan"
        },
        {
            "message": "Show me stocks with TTM Squeeze patterns",
            "description": "Pattern detection request",
            "expect_type": "ttm_squeeze_scan"
        },
        {
            "message": "Analyze TSLA for TTM Squeeze breakout",
            "description": "Specific symbol TTM analysis",
            "expect_type": "guru_analysis"
        },
        {
            "message": "What are the best TTM Squeeze signals today?",
            "description": "Daily TTM opportunities",
            "expect_type": "ttm_squeeze_scan"
        }
    ]
    
    results = []
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: '{test['message']}'")
        print(f"   Expected: {test['description']}")
        print("-" * 50)
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/v1/chat",
                json={
                    "message": test["message"],
                    "session_id": f"ttm-test-{i}",
                    "user_id": "phase3_tester"
                },
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                response_type = data.get('type', 'unknown')
                confidence = data.get('confidence', 0)
                
                print(f"   [OK] Status: {response.status_code}")
                print(f"   [OK] Type: {response_type}")
                print(f"   [OK] Confidence: {confidence:.2f}")
                print(f"   [OK] Response Time: {response_time:.2f}s")
                print(f"   [NOTE] Sample: {response_text[:200]}...")
                
                # Analyze TTM Squeeze content
                ttm_indicators = analyze_ttm_content(response_text)
                print(f"   [TARGET] TTM Content Analysis:")
                for indicator, found in ttm_indicators.items():
                    status = "[OK]" if found else "[ERROR]"
                    print(f"      {status} {indicator}")
                
                results.append({
                    "test": test,
                    "success": True,
                    "response_type": response_type,
                    "confidence": confidence,
                    "response_time": response_time,
                    "ttm_indicators": ttm_indicators,
                    "response_length": len(response_text)
                })
                
            else:
                print(f"   [ERROR] Status: {response.status_code}")
                print(f"   [ERROR] Error: {response.text}")
                results.append({
                    "test": test,
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"   [ERROR] Exception: {e}")
            results.append({
                "test": test,
                "success": False,
                "error": str(e)
            })
        
        time.sleep(2)  # Brief pause between tests
    
    # Generate comprehensive report
    generate_ttm_integration_report(results)
    
    return results

def analyze_ttm_content(response_text: str) -> dict:
    """Analyze response content for TTM Squeeze indicators"""
    text_lower = response_text.lower()
    
    indicators = {
        "TTM Squeeze Mentioned": any(term in text_lower for term in ['ttm squeeze', 'ttm-squeeze', 'squeeze']),
        "Signal Detection": any(term in text_lower for term in ['signal', 'breakout', 'pattern']),
        "Bollinger Bands": any(term in text_lower for term in ['bollinger', 'bb', 'bands']),
        "Keltner Channels": any(term in text_lower for term in ['keltner', 'kc', 'channels']),
        "Momentum Analysis": any(term in text_lower for term in ['momentum', 'histogram', 'acceleration']),
        "Volume Confirmation": any(term in text_lower for term in ['volume', 'surge', 'heavy']),
        "Entry Price": any(term in text_lower for term in ['entry', 'buy at', 'enter at']),
        "Target Price": any(term in text_lower for term in ['target', 'profit', 'upside']),
        "Stop Loss": any(term in text_lower for term in ['stop', 'risk', 'downside']),
        "Confidence Score": any(term in text_lower for term in ['confidence', '%', 'probability']),
        "Symbol Analysis": any(term in text_lower for term in ['aapl', 'tsla', 'msft', 'googl', 'nvda']),
        "Real-time Data": any(term in text_lower for term in ['current', 'real-time', 'live', 'today'])
    }
    
    return indicators

def test_direct_scanner_endpoint():
    """Test the direct scanner endpoint"""
    base_url = "http://localhost:8080"
    
    print(f"\n[SEARCH] TESTING DIRECT SCANNER ENDPOINT")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/api/v1/scan", timeout=20)
        
        if response.status_code == 200:
            data = response.json()
            signals = data.get('signals', [])
            count = data.get('count', 0)
            
            print(f"   [OK] Status: {response.status_code}")
            print(f"   [OK] Signals Found: {count}")
            print(f"   [OK] Response Structure: {list(data.keys())}")
            
            if signals:
                print(f"   [DATA] Sample Signal:")
                sample = signals[0] if isinstance(signals, list) else signals
                print(f"      {json.dumps(sample, indent=6, default=str)}")
            
            return True
            
        else:
            print(f"   [ERROR] Status: {response.status_code}")
            print(f"   [ERROR] Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"   [ERROR] Exception: {e}")
        return False

def generate_ttm_integration_report(results: list):
    """Generate comprehensive TTM integration report"""
    print("\n" + "=" * 60)
    print("📋 TTM SQUEEZE INTEGRATION REPORT")
    print("=" * 60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r.get('success', False))
    
    print(f"[DATA] Overall Results:")
    print(f"   • Total Tests: {total_tests}")
    print(f"   • Successful: {successful_tests}")
    print(f"   • Failed: {total_tests - successful_tests}")
    print(f"   • Success Rate: {successful_tests/total_tests*100:.1f}%")
    
    if successful_tests > 0:
        # Analyze successful tests
        successful_results = [r for r in results if r.get('success', False)]
        
        avg_response_time = sum(r.get('response_time', 0) for r in successful_results) / len(successful_results)
        avg_confidence = sum(r.get('confidence', 0) for r in successful_results) / len(successful_results)
        
        print(f"\n[UP] Performance Metrics:")
        print(f"   • Average Response Time: {avg_response_time:.2f}s")
        print(f"   • Average Confidence: {avg_confidence:.2f}")
        
        # Analyze TTM content coverage
        print(f"\n[TARGET] TTM Squeeze Content Analysis:")
        all_indicators = {}
        for result in successful_results:
            ttm_indicators = result.get('ttm_indicators', {})
            for indicator, found in ttm_indicators.items():
                if indicator not in all_indicators:
                    all_indicators[indicator] = 0
                if found:
                    all_indicators[indicator] += 1
        
        for indicator, count in all_indicators.items():
            percentage = count / len(successful_results) * 100
            status = "[OK]" if percentage >= 60 else "[WARN]" if percentage >= 30 else "[ERROR]"
            print(f"   {status} {indicator}: {count}/{len(successful_results)} ({percentage:.1f}%)")
        
        # Response type analysis
        print(f"\n[NOTE] Response Type Distribution:")
        type_counts = {}
        for result in successful_results:
            response_type = result.get('response_type', 'unknown')
            type_counts[response_type] = type_counts.get(response_type, 0) + 1
        
        for response_type, count in type_counts.items():
            print(f"   • {response_type}: {count} tests")
    
    # Overall assessment
    print(f"\n[TARGET] PHASE 3 TTM SQUEEZE ASSESSMENT:")
    
    if successful_tests == total_tests:
        print("   [SUCCESS] EXCELLENT: All TTM Squeeze tests passed!")
        print("   [OK] Conversational interface fully integrated")
        print("   [OK] Real-time scanning capabilities working")
        print("   [OK] Pattern detection and analysis functional")
    elif successful_tests >= total_tests * 0.8:
        print("   [OK] GOOD: Most TTM Squeeze functionality working")
        print("   [OK] Core scanning capabilities operational")
        print("   [WARN] Minor issues present but system is functional")
    elif successful_tests >= total_tests * 0.5:
        print("   [WARN] PARTIAL: Some TTM Squeeze functionality working")
        print("   [WARN] Core features present but need enhancement")
        print("   [ERROR] Significant improvements needed")
    else:
        print("   [ERROR] NEEDS WORK: TTM Squeeze integration incomplete")
        print("   [ERROR] Major functionality gaps detected")
        print("   [ERROR] Requires substantial development")
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"ttm_squeeze_integration_results_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests/total_tests*100,
            'detailed_results': results
        }, f, indent=2, default=str)
    
    print(f"\n[DOC] Detailed results saved to: {filename}")

def main():
    """Main test execution"""
    print("[LAUNCH] Starting Phase 3: TTM Squeeze Integration Testing...")
    
    # Test conversational integration
    conversational_results = test_ttm_squeeze_conversational_integration()
    
    # Test direct scanner endpoint
    scanner_working = test_direct_scanner_endpoint()
    
    print(f"\n[TARGET] PHASE 3 SUMMARY:")
    print(f"   • Conversational TTM Integration: {len([r for r in conversational_results if r.get('success')])}/{len(conversational_results)} tests passed")
    print(f"   • Direct Scanner Endpoint: {'[OK] Working' if scanner_working else '[ERROR] Issues'}")
    
    if len([r for r in conversational_results if r.get('success')]) >= 4 and scanner_working:
        print(f"   [SUCCESS] PHASE 3: TTM SQUEEZE INTEGRATION - SUCCESS!")
    else:
        print(f"   [WARN] PHASE 3: TTM SQUEEZE INTEGRATION - NEEDS IMPROVEMENT")

if __name__ == "__main__":
    main()
