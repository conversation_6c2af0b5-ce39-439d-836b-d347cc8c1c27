#!/usr/bin/env python3
"""
Comprehensive test to verify the guru response format fix with multiple questions
"""
import requests
import json
import time

def test_question(question, session_id="test_session"):
    """Test a single question and analyze the response"""
    
    url = "http://localhost:8080/api/v1/chat"
    payload = {
        "message": question,
        "session_id": session_id
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get("response", "")
            
            # Check for sophisticated format markers
            sophisticated_markers = [
                "**A.T.L.A.S powered by Predicto - Stock Market God**",
                "**1. Why This Trade?**",
                "**2. Win/Loss Probabilities**",
                "**3. Potential Money In or Out**",
                "**4. Smart Stop Plans**",
                "**5. Market Context**",
                "**6. Confidence Score**",
                "**EXECUTION READY - Trade Plan #"
            ]
            
            found_markers = [marker for marker in sophisticated_markers if marker in response_text]
            
            # Check for generic format markers
            generic_markers = [
                "SPY: $",
                "| ACTION:",
                "| CONFIDENCE:",
                "Entry:",
                "Target:",
                "Stop:"
            ]
            
            found_generic = [marker for marker in generic_markers if marker in response_text]
            
            # Determine response type
            if len(found_markers) >= 4:
                result = "SOPHISTICATED"
            elif len(found_generic) >= 3:
                result = "GENERIC"
            else:
                result = "UNKNOWN"
            
            return {
                "status": response.status_code,
                "result": result,
                "sophisticated_markers": len(found_markers),
                "generic_markers": len(found_generic),
                "response_length": len(response_text),
                "response_preview": response_text[:200] + "..." if len(response_text) > 200 else response_text
            }
        else:
            return {
                "status": response.status_code,
                "result": "ERROR",
                "error": response.text
            }
            
    except Exception as e:
        return {
            "status": "EXCEPTION",
            "result": "ERROR",
            "error": str(e)
        }

def run_comprehensive_test():
    """Run comprehensive test with multiple questions"""
    
    test_questions = [
        "hello",
        "analyze AAPL stock",
        "should I buy Tesla?",
        "what's your trading recommendation for NVDA?",
        "give me a stock pick",
        "analyze SPY for trading",
        "recommend a trade for MSFT",
        "what stocks should I buy today?",
        "trading advice for AMZN",
        "stock analysis for GOOGL"
    ]
    
    print("🧪 Running Comprehensive A.T.L.A.S. Guru Response Test")
    print("=" * 60)
    
    results = []
    sophisticated_count = 0
    generic_count = 0
    error_count = 0
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n[NOTE] Test {i}/10: '{question}'")
        
        result = test_question(question, f"test_session_{i}")
        results.append({"question": question, "result": result})
        
        print(f"   Status: {result['status']}")
        print(f"   Format: {result['result']}")
        
        if result['result'] == "SOPHISTICATED":
            sophisticated_count += 1
            print(f"   [OK] Sophisticated markers: {result['sophisticated_markers']}/8")
        elif result['result'] == "GENERIC":
            generic_count += 1
            print(f"   [ERROR] Generic markers: {result['generic_markers']}/6")
        else:
            error_count += 1
            print(f"   ❓ Unknown/Error: {result.get('error', 'Unknown format')}")
        
        if 'response_length' in result:
            print(f"   📏 Length: {result['response_length']} chars")
        
        # Brief pause between requests
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("[DATA] TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {len(test_questions)}")
    print(f"[OK] Sophisticated Format: {sophisticated_count}")
    print(f"[ERROR] Generic Format: {generic_count}")
    print(f"❓ Errors/Unknown: {error_count}")
    
    success_rate = (sophisticated_count / len(test_questions)) * 100
    print(f"[TARGET] Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("[SUCCESS] SUCCESS: Fix is working! Sophisticated format is being returned.")
        return True
    elif generic_count > 0:
        print("[ERROR] FAILURE: Generic format still being returned. Trading God transformation not bypassed.")
        return False
    else:
        print("❓ UNCLEAR: Mixed results. Need further investigation.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)
