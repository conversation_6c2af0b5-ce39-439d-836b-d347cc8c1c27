#!/usr/bin/env python3
"""
Quick test to verify the guru response format fix
"""
import requests
import json

def test_guru_response():
    """Test if the sophisticated guru response is being returned"""
    
    url = "http://localhost:8080/api/v1/chat"
    payload = {
        "message": "hello",
        "session_id": "test123"
    }
    
    try:
        print("🧪 Testing A.T.L.A.S. Guru Response Format...")
        print(f"📡 Sending request to: {url}")
        print(f"[NOTE] Payload: {json.dumps(payload, indent=2)}")
        print()
        
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"[DATA] Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Check if we got the sophisticated response
            response_text = data.get("response", "")
            
            print("[SEARCH] Response Analysis:")
            print(f"   Response length: {len(response_text)} characters")
            
            # Check for sophisticated format markers
            sophisticated_markers = [
                "**A.T.L.A.S powered by Predicto - Stock Market God**",
                "**1. Why This Trade?**",
                "**2. Win/Loss Probabilities**",
                "**3. Potential Money In or Out**",
                "**4. Smart Stop Plans**",
                "**5. Market Context**",
                "**6. Confidence Score**",
                "**EXECUTION READY - Trade Plan #"
            ]
            
            found_markers = [marker for marker in sophisticated_markers if marker in response_text]
            
            # Check for generic format markers
            generic_markers = [
                "SPY: $",
                "| ACTION:",
                "| CONFIDENCE:",
                "Entry:",
                "Target:",
                "Stop:"
            ]
            
            found_generic = [marker for marker in generic_markers if marker in response_text]
            
            print(f"   Sophisticated markers found: {len(found_markers)}/8")
            print(f"   Generic markers found: {len(found_generic)}/6")
            
            if len(found_markers) >= 4:
                print("[OK] SUCCESS: Sophisticated 6-point format detected!")
                print("[OK] Fix is working - Trading God transformation was bypassed")
            elif len(found_generic) >= 3:
                print("[ERROR] ISSUE: Generic format detected")
                print("[ERROR] Trading God transformation is still overriding the response")
            else:
                print("❓ UNKNOWN: Response format unclear")
            
            print()
            print("[DOC] Full Response:")
            print("=" * 80)
            print(response_text[:1000] + ("..." if len(response_text) > 1000 else ""))
            print("=" * 80)
            
            # Check conversation context if available
            if "context" in data and "conversation_flow" in data["context"]:
                conv_data = data["context"]["conversation_flow"]
                if "conversation_context" in conv_data:
                    conv_context = conv_data["conversation_context"]
                    if hasattr(conv_context, 'conversation_history') and conv_context.conversation_history:
                        last_entry = conv_context.conversation_history[-1]
                        if "system_response" in last_entry:
                            system_response = last_entry["system_response"]
                            print()
                            print("[SEARCH] System Response in Conversation History:")
                            print("=" * 80)
                            print(system_response[:500] + ("..." if len(system_response) > 500 else ""))
                            print("=" * 80)
            
        else:
            print(f"[ERROR] Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
    except requests.exceptions.ConnectionError:
        print("[PLUG] Connection error - is the server running?")
    except Exception as e:
        print(f"[ERROR] Error: {e}")

if __name__ == "__main__":
    test_guru_response()
