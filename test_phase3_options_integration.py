#!/usr/bin/env python3
"""
Phase 3: Test Options Trading Analysis Integration
Validate options analysis integration with conversational AI and 6-point guru format
"""

import requests
import json
import time
from datetime import datetime

def test_options_conversational_integration():
    """Test options trading functionality through conversational interface"""
    base_url = "http://localhost:8080"
    
    print("[TARGET] PHASE 3: OPTIONS TRADING ANALYSIS INTEGRATION TEST")
    print("=" * 60)
    
    test_cases = [
        {
            "message": "What are the best options strategies for AAPL?",
            "description": "Options strategy recommendation",
            "expect_type": "guru_analysis",
            "expect_content": ["options", "strategy", "call", "put", "greeks"]
        },
        {
            "message": "Analyze TSLA call options for next month",
            "description": "Specific options analysis",
            "expect_type": "guru_analysis", 
            "expect_content": ["call", "options", "delta", "theta", "volatility"]
        },
        {
            "message": "Should I buy NVDA puts or calls?",
            "description": "Options direction recommendation",
            "expect_type": "guru_analysis",
            "expect_content": ["put", "call", "recommendation", "volatility"]
        },
        {
            "message": "Explain covered call strategy for MSFT",
            "description": "Options strategy education",
            "expect_type": "guru_analysis",
            "expect_content": ["covered call", "premium", "strike", "income"]
        },
        {
            "message": "Find high IV options opportunities",
            "description": "Volatility-based options scan",
            "expect_type": "guru_analysis",
            "expect_content": ["implied volatility", "iv", "premium", "opportunities"]
        }
    ]
    
    results = []
    
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: '{test['message']}'")
        print(f"   Expected: {test['description']}")
        print("-" * 50)
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/v1/chat",
                json={
                    "message": test["message"],
                    "session_id": f"options-test-{i}",
                    "user_id": "phase3_options_tester"
                },
                timeout=30
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                response_type = data.get('type', 'unknown')
                confidence = data.get('confidence', 0)
                
                print(f"   [OK] Status: {response.status_code}")
                print(f"   [OK] Type: {response_type}")
                print(f"   [OK] Confidence: {confidence:.2f}")
                print(f"   [OK] Response Time: {response_time:.2f}s")
                print(f"   [NOTE] Sample: {response_text[:200]}...")
                
                # Analyze options content
                options_indicators = analyze_options_content(response_text, test["expect_content"])
                print(f"   [TARGET] Options Content Analysis:")
                for indicator, found in options_indicators.items():
                    status = "[OK]" if found else "[ERROR]"
                    print(f"      {status} {indicator}")
                
                # Check for 6-point format in trading responses
                six_point_analysis = analyze_six_point_format(response_text)
                print(f"   [DATA] 6-Point Format Analysis:")
                for point, found in six_point_analysis.items():
                    status = "[OK]" if found else "[ERROR]"
                    print(f"      {status} {point}")
                
                results.append({
                    "test": test,
                    "success": True,
                    "response_type": response_type,
                    "confidence": confidence,
                    "response_time": response_time,
                    "options_indicators": options_indicators,
                    "six_point_format": six_point_analysis,
                    "response_length": len(response_text)
                })
                
            else:
                print(f"   [ERROR] Status: {response.status_code}")
                print(f"   [ERROR] Error: {response.text}")
                results.append({
                    "test": test,
                    "success": False,
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"   [ERROR] Exception: {e}")
            results.append({
                "test": test,
                "success": False,
                "error": str(e)
            })
        
        time.sleep(2)  # Brief pause between tests
    
    # Generate comprehensive report
    generate_options_integration_report(results)
    
    return results

def analyze_options_content(response_text: str, expected_content: list) -> dict:
    """Analyze response content for options trading indicators"""
    text_lower = response_text.lower()
    
    indicators = {
        "Options Mentioned": any(term in text_lower for term in ['option', 'options']),
        "Strategy Analysis": any(term in text_lower for term in ['strategy', 'covered call', 'put', 'call', 'straddle', 'strangle']),
        "Greeks Analysis": any(term in text_lower for term in ['delta', 'gamma', 'theta', 'vega', 'rho', 'greeks']),
        "Volatility Analysis": any(term in text_lower for term in ['volatility', 'iv', 'implied volatility', 'vol']),
        "Strike Price": any(term in text_lower for term in ['strike', 'strike price', 'otm', 'itm', 'atm']),
        "Expiration": any(term in text_lower for term in ['expiration', 'expiry', 'dte', 'days to expiration']),
        "Premium Analysis": any(term in text_lower for term in ['premium', 'cost', 'price', 'bid', 'ask']),
        "Risk/Reward": any(term in text_lower for term in ['risk', 'reward', 'profit', 'loss', 'breakeven']),
        "Expected Content": all(term.lower() in text_lower for term in expected_content)
    }
    
    return indicators

def analyze_six_point_format(response_text: str) -> dict:
    """Analyze if response follows 6-point Stock Market Guru format"""
    text_lower = response_text.lower()
    
    six_point_format = {
        "1. Why This Trade": any(term in text_lower for term in ['why this trade', 'rationale', 'reason', 'analysis']),
        "2. Win/Loss Probabilities": any(term in text_lower for term in ['probability', 'chance', 'likely', '%', 'odds']),
        "3. Money In/Out": any(term in text_lower for term in ['$', 'cost', 'premium', 'investment', 'capital']),
        "4. Stop Plans": any(term in text_lower for term in ['stop', 'exit', 'loss', 'risk management']),
        "5. Market Context": any(term in text_lower for term in ['market', 'trend', 'context', 'environment']),
        "6. Confidence Score": any(term in text_lower for term in ['confidence', 'score', '%', 'certainty'])
    }
    
    return six_point_format

def test_direct_options_endpoint():
    """Test the direct options analysis endpoint"""
    base_url = "http://localhost:8080"
    
    print(f"\n[SEARCH] TESTING DIRECT OPTIONS ENDPOINT")
    print("=" * 50)
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/options/analyze",
            json={
                "symbol": "AAPL",
                "option_type": "call",
                "strike_price": 180.0,
                "expiration_days": 30
            },
            timeout=20
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"   [OK] Status: {response.status_code}")
            print(f"   [OK] Response Structure: {list(data.keys())}")
            
            if 'greeks' in data:
                greeks = data['greeks']
                print(f"   [DATA] Greeks Analysis:")
                print(f"      Delta: {greeks.get('delta', 'N/A')}")
                print(f"      Gamma: {greeks.get('gamma', 'N/A')}")
                print(f"      Theta: {greeks.get('theta', 'N/A')}")
                print(f"      Vega: {greeks.get('vega', 'N/A')}")
            
            if 'option_price' in data:
                print(f"   [MONEY] Option Price: ${data['option_price']:.2f}")
            
            if 'strategy_recommendation' in data:
                print(f"   [TARGET] Strategy: {data['strategy_recommendation']}")
            
            return True
            
        else:
            print(f"   [ERROR] Status: {response.status_code}")
            print(f"   [ERROR] Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"   [ERROR] Exception: {e}")
        return False

def generate_options_integration_report(results: list):
    """Generate comprehensive options integration report"""
    print("\n" + "=" * 60)
    print("📋 OPTIONS TRADING ANALYSIS INTEGRATION REPORT")
    print("=" * 60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r.get('success', False))
    
    print(f"[DATA] Overall Results:")
    print(f"   • Total Tests: {total_tests}")
    print(f"   • Successful: {successful_tests}")
    print(f"   • Failed: {total_tests - successful_tests}")
    print(f"   • Success Rate: {successful_tests/total_tests*100:.1f}%")
    
    if successful_tests > 0:
        # Analyze successful tests
        successful_results = [r for r in results if r.get('success', False)]
        
        avg_response_time = sum(r.get('response_time', 0) for r in successful_results) / len(successful_results)
        avg_confidence = sum(r.get('confidence', 0) for r in successful_results) / len(successful_results)
        
        print(f"\n[UP] Performance Metrics:")
        print(f"   • Average Response Time: {avg_response_time:.2f}s")
        print(f"   • Average Confidence: {avg_confidence:.2f}")
        
        # Analyze options content coverage
        print(f"\n[TARGET] Options Trading Content Analysis:")
        all_indicators = {}
        for result in successful_results:
            options_indicators = result.get('options_indicators', {})
            for indicator, found in options_indicators.items():
                if indicator not in all_indicators:
                    all_indicators[indicator] = 0
                if found:
                    all_indicators[indicator] += 1
        
        for indicator, count in all_indicators.items():
            percentage = count / len(successful_results) * 100
            status = "[OK]" if percentage >= 60 else "[WARN]" if percentage >= 30 else "[ERROR]"
            print(f"   {status} {indicator}: {count}/{len(successful_results)} ({percentage:.1f}%)")
        
        # Analyze 6-point format compliance
        print(f"\n[DATA] 6-Point Format Compliance:")
        all_six_point = {}
        for result in successful_results:
            six_point = result.get('six_point_format', {})
            for point, found in six_point.items():
                if point not in all_six_point:
                    all_six_point[point] = 0
                if found:
                    all_six_point[point] += 1
        
        for point, count in all_six_point.items():
            percentage = count / len(successful_results) * 100
            status = "[OK]" if percentage >= 60 else "[WARN]" if percentage >= 30 else "[ERROR]"
            print(f"   {status} {point}: {count}/{len(successful_results)} ({percentage:.1f}%)")
        
        # Response type analysis
        print(f"\n[NOTE] Response Type Distribution:")
        type_counts = {}
        for result in successful_results:
            response_type = result.get('response_type', 'unknown')
            type_counts[response_type] = type_counts.get(response_type, 0) + 1
        
        for response_type, count in type_counts.items():
            print(f"   • {response_type}: {count} tests")
    
    # Overall assessment
    print(f"\n[TARGET] PHASE 3 OPTIONS ANALYSIS ASSESSMENT:")
    
    if successful_tests == total_tests:
        print("   [SUCCESS] EXCELLENT: All options analysis tests passed!")
        print("   [OK] Options strategies fully integrated with conversational AI")
        print("   [OK] Greeks calculations and volatility analysis working")
        print("   [OK] 6-point guru format applied to options recommendations")
    elif successful_tests >= total_tests * 0.8:
        print("   [OK] GOOD: Most options functionality working")
        print("   [OK] Core options analysis capabilities operational")
        print("   [WARN] Minor issues present but system is functional")
    elif successful_tests >= total_tests * 0.5:
        print("   [WARN] PARTIAL: Some options functionality working")
        print("   [WARN] Core features present but need enhancement")
        print("   [ERROR] Significant improvements needed")
    else:
        print("   [ERROR] NEEDS WORK: Options integration incomplete")
        print("   [ERROR] Major functionality gaps detected")
        print("   [ERROR] Requires substantial development")
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"options_integration_results_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'total_tests': total_tests,
            'successful_tests': successful_tests,
            'success_rate': successful_tests/total_tests*100,
            'detailed_results': results
        }, f, indent=2, default=str)
    
    print(f"\n[DOC] Detailed results saved to: {filename}")

def main():
    """Main test execution"""
    print("[LAUNCH] Starting Phase 3: Options Trading Analysis Integration Testing...")
    
    # Test conversational integration
    conversational_results = test_options_conversational_integration()
    
    # Test direct options endpoint
    options_endpoint_working = test_direct_options_endpoint()
    
    print(f"\n[TARGET] PHASE 3 OPTIONS SUMMARY:")
    print(f"   • Conversational Options Integration: {len([r for r in conversational_results if r.get('success')])}/{len(conversational_results)} tests passed")
    print(f"   • Direct Options Endpoint: {'[OK] Working' if options_endpoint_working else '[ERROR] Issues'}")
    
    if len([r for r in conversational_results if r.get('success')]) >= 4 and options_endpoint_working:
        print(f"   [SUCCESS] PHASE 3: OPTIONS TRADING ANALYSIS - SUCCESS!")
    else:
        print(f"   [WARN] PHASE 3: OPTIONS TRADING ANALYSIS - NEEDS IMPROVEMENT")

if __name__ == "__main__":
    main()
