#!/usr/bin/env python3
"""
Test current A.T.L.A.S. responses to identify templated vs dynamic content
"""
import requests
import json
import time

def test_current_responses():
    """Test current response quality and identify template issues"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_cases = [
        {
            "name": "Casual Greeting",
            "message": "whats up?",
            "expected_style": "Brief, casual response"
        },
        {
            "name": "Formal Greeting", 
            "message": "hello",
            "expected_style": "Friendly welcome"
        },
        {
            "name": "Capability Question",
            "message": "What can you do?",
            "expected_style": "Concise, helpful explanation"
        },
        {
            "name": "Stock Analysis",
            "message": "analyze aapl",
            "expected_style": "Real market analysis with actual data"
        },
        {
            "name": "General Question",
            "message": "How are you doing?",
            "expected_style": "Natural conversational response"
        }
    ]
    
    print("[SEARCH] Testing Current A.T.L.A.S. Response Quality")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[NOTE] Test {i}: {test_case['name']}")
        print(f"Message: '{test_case['message']}'")
        print(f"Expected: {test_case['expected_style']}")
        print("-" * 50)
        
        payload = {
            "message": test_case["message"],
            "session_id": f"response_test_{i}",
            "user_id": "test_user"
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, json=payload, timeout=15)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                response_type = data.get('type', 'unknown')
                confidence = data.get('confidence', 0.0)
                
                print(f"[OK] Response received")
                print(f"[DATA] Type: {response_type}")
                print(f"[TARGET] Confidence: {confidence}")
                print(f"📏 Length: {len(response_text)} characters")
                print(f"⏱️  Time: {end_time - start_time:.2f}s")
                
                # Analyze response characteristics
                print(f"\n💬 Full Response:")
                print(f"'{response_text}'")
                
                # Check for template indicators
                template_indicators = [
                    "**A.T.L.A.S powered by Predicto - Stock Market God**",
                    "Entry Strategy:",
                    "Position value:",
                    "I'm A.T.L.A.S. powered by Predicto, and I can help you with:",
                    "Core Trading Features:",
                    "Hello! I'm A.T.L.A.S.",
                    "What would you like to explore"
                ]
                
                found_templates = [indicator for indicator in template_indicators if indicator in response_text]
                
                if found_templates:
                    print(f"\n[WARN]  Template indicators found: {len(found_templates)}")
                    for template in found_templates:
                        print(f"   - '{template[:50]}...'")
                else:
                    print(f"\n[OK] No obvious template indicators detected")
                
                # Check response appropriateness
                if test_case['name'] == "Casual Greeting" and len(response_text) > 200:
                    print(f"[ERROR] Response too long for casual greeting ({len(response_text)} chars)")
                elif test_case['name'] == "Stock Analysis" and "AAPL" not in response_text.upper():
                    print(f"[ERROR] Stock analysis doesn't mention requested symbol")
                elif "SPY:" in response_text and test_case['message'] != "analyze spy":
                    print(f"[ERROR] Response contains wrong stock symbol (SPY instead of requested)")
                else:
                    print(f"[OK] Response length and content seem appropriate")
                    
            else:
                print(f"[ERROR] HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"[ERROR] Error: {e}")
        
        print("\n" + "="*60)
        time.sleep(2)  # Small delay between tests

if __name__ == "__main__":
    test_current_responses()
