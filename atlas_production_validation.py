#!/usr/bin/env python3
"""
A.T.L.A.S. Production Validation
Final comprehensive test to demonstrate ChatGPT-style conversational capabilities
"""

import requests
import json
import time
from datetime import datetime

class ATLASProductionValidator:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.session_id = f"prod-validation-{int(time.time())}"
        self.results = []
        
    def log_result(self, test_name, success, details="", response_sample=""):
        """Log validation result"""
        status = "[OK] PASS" if success else "[ERROR] FAIL"
        print(f"{status} | {test_name}")
        if details:
            print(f"     {details}")
        if response_sample:
            print(f"     Sample: {response_sample[:100]}...")
        
        self.results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "response_sample": response_sample[:200] if response_sample else "",
            "timestamp": datetime.now().isoformat()
        })
        
        return success

    def validate_conversational_flow(self):
        """Validate natural conversational capabilities"""
        print("\n[TALK]  CONVERSATIONAL AI VALIDATION")
        print("=" * 50)
        
        conversation_tests = [
            {
                "message": "Hello, I'm new to trading",
                "expect": "conversational_greeting",
                "check": lambda r: "hello" in r.lower() or "hi" in r.lower() or "welcome" in r.lower()
            },
            {
                "message": "What can you help me with?",
                "expect": "capability_overview",
                "check": lambda r: any(term in r.lower() for term in ["analyze", "trading", "market", "stock"])
            },
            {
                "message": "Thanks for the help",
                "expect": "polite_response",
                "check": lambda r: any(term in r.lower() for term in ["welcome", "happy", "glad", "anytime"])
            }
        ]
        
        passed = 0
        for test in conversation_tests:
            try:
                response = requests.post(
                    f"{self.base_url}/api/v1/chat",
                    json={"message": test["message"], "session_id": self.session_id},
                    timeout=20
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get('response', '')
                    
                    # Check if response is conversational (not 6-point format)
                    is_conversational = not any(term in response_text.lower() for term in ['why this trade', 'win/loss probabilities'])
                    
                    # Check specific expectation
                    meets_expectation = test["check"](response_text)
                    
                    success = is_conversational and meets_expectation
                    if self.log_result(
                        f"Conversational: {test['expect']}", 
                        success,
                        f"Type: {data.get('type', 'unknown')}, Conversational: {is_conversational}, Expectation: {meets_expectation}",
                        response_text
                    ):
                        passed += 1
                else:
                    self.log_result(f"Conversational: {test['expect']}", False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"Conversational: {test['expect']}", False, f"Error: {e}")
        
        return passed, len(conversation_tests)

    def validate_trading_analysis(self):
        """Validate Stock Market Guru 6-point format"""
        print("\n[UP] TRADING ANALYSIS VALIDATION")
        print("=" * 50)
        
        trading_tests = [
            {
                "message": "Analyze AAPL for trading",
                "symbol": "AAPL",
                "expect": "6_point_analysis"
            },
            {
                "message": "Should I buy Tesla stock?",
                "symbol": "TSLA",
                "expect": "trading_recommendation"
            }
        ]
        
        passed = 0
        for test in trading_tests:
            try:
                response = requests.post(
                    f"{self.base_url}/api/v1/chat",
                    json={"message": test["message"], "session_id": f"{self.session_id}-trading"},
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get('response', '')
                    
                    # Check for 6-point format elements
                    six_point_elements = [
                        'why this trade', 'win/loss', 'money in', 'stop plan', 'market context', 'confidence'
                    ]
                    found_elements = sum(1 for element in six_point_elements if element in response_text.lower())
                    
                    # Check for trading-specific content
                    has_trading_content = any(term in response_text.lower() for term in [
                        'buy', 'sell', 'price', 'target', 'stop', 'risk', 'profit'
                    ])
                    
                    # Check for specific dollar amounts or percentages
                    has_specifics = any(char in response_text for char in ['$', '%'])
                    
                    success = found_elements >= 3 and has_trading_content and has_specifics
                    if self.log_result(
                        f"Trading Analysis: {test['symbol']}", 
                        success,
                        f"6-Point Elements: {found_elements}/6, Trading Content: {has_trading_content}, Specifics: {has_specifics}",
                        response_text
                    ):
                        passed += 1
                else:
                    self.log_result(f"Trading Analysis: {test['symbol']}", False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"Trading Analysis: {test['symbol']}", False, f"Error: {e}")
        
        return passed, len(trading_tests)

    def validate_context_awareness(self):
        """Validate conversation context and memory"""
        print("\n[BRAIN] CONTEXT AWARENESS VALIDATION")
        print("=" * 50)
        
        context_session = f"{self.session_id}-context"
        
        # First establish context
        try:
            setup_response = requests.post(
                f"{self.base_url}/api/v1/chat",
                json={"message": "I'm interested in Apple stock", "session_id": context_session},
                timeout=20
            )
            
            if setup_response.status_code == 200:
                # Now test follow-up that requires context
                followup_response = requests.post(
                    f"{self.base_url}/api/v1/chat",
                    json={"message": "What do you think about it?", "session_id": context_session},
                    timeout=20
                )
                
                if followup_response.status_code == 200:
                    followup_data = followup_response.json()
                    followup_text = followup_data.get('response', '')
                    
                    # Check if it references Apple/AAPL from previous context
                    references_apple = any(term in followup_text.lower() for term in ['apple', 'aapl'])
                    
                    success = references_apple
                    self.log_result(
                        "Context Awareness", 
                        success,
                        f"References Apple: {references_apple}",
                        followup_text
                    )
                    return 1 if success else 0, 1
                else:
                    self.log_result("Context Awareness", False, f"Follow-up HTTP {followup_response.status_code}")
            else:
                self.log_result("Context Awareness", False, f"Setup HTTP {setup_response.status_code}")
                
        except Exception as e:
            self.log_result("Context Awareness", False, f"Error: {e}")
        
        return 0, 1

    def validate_system_integration(self):
        """Validate overall system integration"""
        print("\n[TOOL] SYSTEM INTEGRATION VALIDATION")
        print("=" * 50)
        
        integration_tests = [
            {
                "endpoint": "/api/v1/health",
                "method": "GET",
                "expect": "system_health"
            },
            {
                "endpoint": "/api/v1/quote/AAPL",
                "method": "GET", 
                "expect": "market_data"
            },
            {
                "endpoint": "/api/v1/scan",
                "method": "GET",
                "expect": "market_scanner"
            }
        ]
        
        passed = 0
        for test in integration_tests:
            try:
                if test["method"] == "GET":
                    response = requests.get(f"{self.base_url}{test['endpoint']}", timeout=10)
                else:
                    response = requests.post(f"{self.base_url}{test['endpoint']}", timeout=10)
                
                success = response.status_code == 200
                details = f"HTTP {response.status_code}"
                
                if success and test["expect"] == "system_health":
                    data = response.json()
                    engines = data.get('engines', {})
                    active_engines = sum(1 for status in engines.values() if status == 'active')
                    details += f", Engines: {active_engines}/{len(engines)}"
                
                if self.log_result(f"Integration: {test['expect']}", success, details):
                    passed += 1
                    
            except Exception as e:
                self.log_result(f"Integration: {test['expect']}", False, f"Error: {e}")
        
        return passed, len(integration_tests)

    def run_production_validation(self):
        """Run complete production validation"""
        print("[LAUNCH] A.T.L.A.S. PRODUCTION VALIDATION")
        print("=" * 60)
        print("Testing ChatGPT-style conversational capabilities with trading expertise")
        
        # Run all validation categories
        conv_passed, conv_total = self.validate_conversational_flow()
        trading_passed, trading_total = self.validate_trading_analysis()
        context_passed, context_total = self.validate_context_awareness()
        integration_passed, integration_total = self.validate_system_integration()
        
        # Calculate overall results
        total_passed = conv_passed + trading_passed + context_passed + integration_passed
        total_tests = conv_total + trading_total + context_total + integration_total
        pass_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # Generate final report
        print("\n" + "=" * 60)
        print("📋 PRODUCTION VALIDATION REPORT")
        print("=" * 60)
        
        print(f"[DATA] Overall Results:")
        print(f"   • Total Tests: {total_tests}")
        print(f"   • Passed: {total_passed}")
        print(f"   • Failed: {total_tests - total_passed}")
        print(f"   • Pass Rate: {pass_rate:.1f}%")
        
        print(f"\n[UP] Category Breakdown:")
        print(f"   • Conversational AI: {conv_passed}/{conv_total} ({conv_passed/conv_total*100:.0f}%)")
        print(f"   • Trading Analysis: {trading_passed}/{trading_total} ({trading_passed/trading_total*100:.0f}%)")
        print(f"   • Context Awareness: {context_passed}/{context_total} ({context_passed/context_total*100:.0f}%)")
        print(f"   • System Integration: {integration_passed}/{integration_total} ({integration_passed/integration_total*100:.0f}%)")
        
        if pass_rate >= 90:
            print("\n[SUCCESS] PRODUCTION READY!")
            print("   [OK] A.T.L.A.S. demonstrates true ChatGPT-style conversational capabilities")
            print("   [OK] Context-aware responses with trading expertise")
            print("   [OK] Dynamic OpenAI integration (no static fallbacks)")
            print("   [OK] Professional 6-point trading analysis format")
            print("   [OK] Natural conversation flow and context memory")
        elif pass_rate >= 75:
            print("\n[OK] MOSTLY FUNCTIONAL")
            print("   [OK] Core conversational capabilities working")
            print("   [WARN] Minor issues present but system operational")
        else:
            print("\n[WARN] NEEDS ADDITIONAL WORK")
            print("   [ERROR] Some core functionality not working as expected")
        
        # Save detailed results
        with open('atlas_production_validation_results.json', 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'pass_rate': pass_rate,
                'total_passed': total_passed,
                'total_tests': total_tests,
                'category_results': {
                    'conversational': {'passed': conv_passed, 'total': conv_total},
                    'trading': {'passed': trading_passed, 'total': trading_total},
                    'context': {'passed': context_passed, 'total': context_total},
                    'integration': {'passed': integration_passed, 'total': integration_total}
                },
                'detailed_results': self.results
            }, f, indent=2)
        
        print(f"\n[DOC] Detailed results saved to: atlas_production_validation_results.json")
        
        return pass_rate >= 75

if __name__ == "__main__":
    validator = ATLASProductionValidator()
    is_production_ready = validator.run_production_validation()
    
    if is_production_ready:
        print("\n[TARGET] A.T.L.A.S. is ready for production use!")
    else:
        print("\n[TOOL] A.T.L.A.S. needs additional fixes before production.")
