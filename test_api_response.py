#!/usr/bin/env python3
"""
Test script to verify A.T.L.A.S. API responses and identify response pipeline issues
"""

import requests
import json
import sys
import time

def test_atlas_api():
    """Test the A.T.L.A.S. chat API and analyze response content"""

    url = "http://localhost:8080/api/v1/chat"

    test_messages = [
        "hello",
        "what can you do?",
        "analyze NVDA stock"
    ]

    print("[SEARCH] DEBUGGING A.T.L.A.S. API RESPONSE PIPELINE")
    print("=" * 60)
    print("Investigating disconnect between backend processing and frontend responses...")
    print()

    for i, message in enumerate(test_messages, 1):
        print(f"📤 TEST {i}: '{message}'")
        print("-" * 40)

        payload = {
            "message": message,
            "session_id": f"debug_session_{i}_{int(time.time())}"
        }

        try:
            print(f"[WEB] Sending request to: {url}")
            print(f"📦 Payload: {json.dumps(payload, indent=2)}")

            response = requests.post(url, json=payload, timeout=30)

            print(f"[DATA] Status Code: {response.status_code}")
            print(f"📋 Response Headers: {dict(response.headers)}")

            if response.status_code == 200:
                print(f"[DOC] Raw Response Text: {response.text}")
                print()

                try:
                    response_data = response.json()
                    print(f"[SEARCH] Response Data Type: {type(response_data)}")
                    print(f"🗂️  Response Keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")

                    if isinstance(response_data, dict):
                        print(f"[NOTE] Full JSON Response:")
                        print(json.dumps(response_data, indent=2))

                        if 'response' in response_data:
                            response_content = response_data['response']
                            print(f"\n[TARGET] EXTRACTED RESPONSE CONTENT:")
                            print(f"'{response_content}'")

                            # Check if it's the generic fallback
                            if "SPY:" in response_content and "ACTION: SCAN" in response_content:
                                print("[ERROR] DETECTED GENERIC FALLBACK RESPONSE!")
                                print("   This is NOT the sophisticated Predicto AI response.")
                            else:
                                print("[OK] This appears to be a proper AI-generated response.")
                        else:
                            print("[WARN]  No 'response' key found in JSON response")
                    else:
                        print(f"[WARN]  Response is not a JSON object: {response_data}")

                except json.JSONDecodeError as e:
                    print(f"[ERROR] JSON Decode Error: {e}")
                    print(f"[DOC] Raw text response: {response.text}")
            else:
                print(f"[ERROR] HTTP Error: {response.status_code}")
                print(f"[DOC] Error Text: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"[ERROR] Request Exception: {e}")

        print("\n" + "="*60 + "\n")
        time.sleep(1)  # Brief pause between requests

if __name__ == "__main__":
    test_atlas_api()
