#!/usr/bin/env python3
"""
A.T.L.A.S. Unicode Fix Script
This script will replace all Unicode characters in logging statements with ASCII equivalents
"""

import os
import re
import glob
from typing import Dict, List, Tuple

# Unicode to ASCII mapping
UNICODE_REPLACEMENTS = {
    # Core system emojis
    '[AI]': '[AI]',
    '[OK]': '[OK]',
    '[ERROR]': '[ERROR]',
    '[WARN]': '[WARN]',
    '[DATA]': '[DATA]',
    '[LAUNCH]': '[LAUNCH]',
    '[TRADE]': '[TRADE]',
    '[TARGET]': '[TARGET]',
    '[SEARCH]': '[SEARCH]',
    '[UP]': '[UP]',
    '[DOWN]': '[DOWN]',
    '[MONEY]': '[MONEY]',
    '[SHIELD]': '[SHIELD]',
    '[STAR]': '[STAR]',
    '[TOOL]': '[TOOL]',
    '[WEB]': '[WEB]',
    '[NOTE]': '[NOTE]',
    '[FINISH]': '[FINISH]',
    '[SUCCESS]': '[SUCCESS]',
    '[LINK]': '[LINK]',
    '[BRAIN]': '[BRAIN]',
    '[BOOK]': '[BOOK]',
    '[LIBRARY]': '[LIBRARY]',
    '[DOC]': '[DOC]',
    '[IDEA]': '[IDEA]',
    '[FAST]': '[FAST]',
    
    # Additional emojis found in codebase
    '[TALK]': '[TALK]',
    '[DELETE]': '[DELETE]',
    '[INFO]': '[INFO]',
    '[BOT]': '[BOT]',
    
    # Technology emojis
    '[LAPTOP]': '[LAPTOP]',
    '[DESKTOP]': '[DESKTOP]',
    '[PRINTER]': '[PRINTER]',
    '[KEYBOARD]': '[KEYBOARD]',
    '[MOUSE]': '[MOUSE]',
    '[TRACKBALL]': '[TRACKBALL]',
    '[DISK]': '[DISK]',
    '[FLOPPY]': '[FLOPPY]',
    '[CD]': '[CD]',
    '[DVD]': '[DVD]',
    '[PHONE]': '[PHONE]',
    '[TELEPHONE]': '[TELEPHONE]',
    '[RECEIVER]': '[RECEIVER]',
    '[PAGER]': '[PAGER]',
    '[FAX]': '[FAX]',
    '[BATTERY]': '[BATTERY]',
    '[PLUG]': '[PLUG]'
}

def find_python_files(directory: str) -> List[str]:
    """Find all Python files in the directory"""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Skip certain directories
        skip_dirs = ['.git', '__pycache__', '.pytest_cache', 'venv', 'env']
        dirs[:] = [d for d in dirs if d not in skip_dirs]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def find_unicode_in_logging(file_path: str) -> List[Tuple[int, str, str]]:
    """Find Unicode characters in logging statements"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            # Check if line contains logging statement
            if re.search(r'logger\.(info|debug|warning|error|critical)', line):
                # Check for Unicode characters
                for unicode_char, replacement in UNICODE_REPLACEMENTS.items():
                    if unicode_char in line:
                        issues.append((line_num, line.strip(), unicode_char))
    
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return issues

def fix_unicode_in_file(file_path: str) -> bool:
    """Fix Unicode characters in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Replace Unicode characters
        for unicode_char, replacement in UNICODE_REPLACEMENTS.items():
            content = content.replace(unicode_char, replacement)
        
        # Only write if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
    
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix Unicode issues"""
    print("A.T.L.A.S. Unicode Fix Script")
    print("=" * 50)
    
    # Get current directory
    current_dir = os.getcwd()
    print(f"Scanning directory: {current_dir}")
    
    # Find all Python files
    python_files = find_python_files(current_dir)
    print(f"Found {len(python_files)} Python files")
    
    # Scan for Unicode issues
    total_issues = 0
    files_with_issues = []
    
    print("\nScanning for Unicode characters in logging statements...")
    for file_path in python_files:
        issues = find_unicode_in_logging(file_path)
        if issues:
            files_with_issues.append((file_path, issues))
            total_issues += len(issues)
    
    print(f"\nFound {total_issues} Unicode issues in {len(files_with_issues)} files")
    
    if files_with_issues:
        print("\nFiles with Unicode issues:")
        for file_path, issues in files_with_issues:
            rel_path = os.path.relpath(file_path, current_dir)
            print(f"  {rel_path}: {len(issues)} issues")
        
        # Ask for confirmation
        response = input("\nDo you want to fix these issues? (y/N): ")
        if response.lower() in ['y', 'yes']:
            print("\nFixing Unicode issues...")
            fixed_files = 0
            
            for file_path in python_files:
                if fix_unicode_in_file(file_path):
                    rel_path = os.path.relpath(file_path, current_dir)
                    print(f"  Fixed: {rel_path}")
                    fixed_files += 1
            
            print(f"\nFixed {fixed_files} files")
            print("Unicode fix completed!")
        else:
            print("Fix cancelled.")
    else:
        print("No Unicode issues found!")

if __name__ == "__main__":
    main()
