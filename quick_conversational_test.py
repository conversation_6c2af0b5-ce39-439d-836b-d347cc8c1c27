#!/usr/bin/env python3
"""
Quick test to verify conversational intelligence is working
"""
import requests
import json

def test_quick():
    url = "http://localhost:8080/api/v1/chat"
    
    tests = [
        {"message": "hello", "expected": "greeting"},
        {"message": "What can you do?", "expected": "capabilities"},
        {"message": "Analyze AAPL", "expected": "guru_trade_plan"}
    ]
    
    for test in tests:
        payload = {
            "message": test["message"],
            "session_id": f"quick_test_{test['message'][:5]}"
        }
        
        try:
            response = requests.post(url, json=payload, timeout=10)
            if response.status_code == 200:
                data = response.json()
                response_type = data.get('type', 'unknown')
                response_text = data.get('response', '')
                
                print(f"Message: '{test['message']}'")
                print(f"Expected: {test['expected']}")
                print(f"Actual: {response_type}")
                print(f"Match: {'[OK]' if response_type == test['expected'] else '[ERROR]'}")
                print(f"Preview: {response_text[:100]}...")
                print("-" * 50)
            else:
                print(f"Error: {response.status_code}")
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    test_quick()
