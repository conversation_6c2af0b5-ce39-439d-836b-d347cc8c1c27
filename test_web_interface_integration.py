#!/usr/bin/env python3
"""
Test Web Interface Integration
Validate that the HTML interface works properly with the enhanced backend
"""

import requests
import json
import time
from datetime import datetime
import webbrowser
import os

class WebInterfaceValidator:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.session_id = f"web-test-{int(time.time())}"
        self.results = []
        
    def log_result(self, test_name, success, details="", response_sample=""):
        """Log validation result"""
        status = "[OK] PASS" if success else "[ERROR] FAIL"
        print(f"{status} | {test_name}")
        if details:
            print(f"     {details}")
        if response_sample:
            print(f"     Sample: {response_sample[:100]}...")
        
        self.results.append({
            "test": test_name,
            "success": success,
            "details": details,
            "response_sample": response_sample[:200] if response_sample else "",
            "timestamp": datetime.now().isoformat()
        })
        
        return success

    def test_api_endpoints(self):
        """Test all API endpoints that the web interface uses"""
        print("\n[PLUG] TESTING API ENDPOINTS")
        print("=" * 50)
        
        endpoints = [
            {
                "name": "Health Check",
                "method": "GET",
                "url": "/api/v1/health",
                "expect": "system status"
            },
            {
                "name": "Chat API",
                "method": "POST",
                "url": "/api/v1/chat",
                "data": {
                    "message": "Hello from web interface",
                    "session_id": self.session_id,
                    "user_id": "web_user",
                    "context": {"interface": "web", "timestamp": datetime.now().isoformat()}
                },
                "expect": "conversational response"
            },
            {
                "name": "Quote API",
                "method": "GET", 
                "url": "/api/v1/quote/AAPL",
                "expect": "stock quote data"
            },
            {
                "name": "Market Scanner",
                "method": "GET",
                "url": "/api/v1/scan",
                "expect": "market scan results"
            }
        ]
        
        passed = 0
        for endpoint in endpoints:
            try:
                if endpoint["method"] == "GET":
                    response = requests.get(f"{self.base_url}{endpoint['url']}", timeout=10)
                else:
                    response = requests.post(
                        f"{self.base_url}{endpoint['url']}", 
                        json=endpoint.get("data", {}),
                        timeout=15
                    )
                
                success = response.status_code == 200
                details = f"HTTP {response.status_code}"
                
                if success:
                    try:
                        data = response.json()
                        if endpoint["name"] == "Chat API":
                            response_text = data.get('response', '')
                            details += f", Response length: {len(response_text)}"
                        elif endpoint["name"] == "Quote API":
                            price = data.get('price', 'N/A')
                            details += f", Price: ${price}"
                        elif endpoint["name"] == "Health Check":
                            status = data.get('status', 'unknown')
                            details += f", Status: {status}"
                    except:
                        details += ", JSON parse error"
                
                if self.log_result(f"API: {endpoint['name']}", success, details):
                    passed += 1
                    
            except Exception as e:
                self.log_result(f"API: {endpoint['name']}", False, f"Error: {e}")
        
        return passed, len(endpoints)

    def test_conversational_flow_via_api(self):
        """Test conversational flow through the API (simulating web interface)"""
        print("\n💬 TESTING CONVERSATIONAL FLOW VIA API")
        print("=" * 50)
        
        conversation_tests = [
            {
                "message": "Hello, I'm using the web interface",
                "expect_type": "greeting",
                "check": lambda r: any(word in r.lower() for word in ["hello", "hi", "welcome"])
            },
            {
                "message": "What can you help me with?",
                "expect_type": "capabilities",
                "check": lambda r: any(word in r.lower() for word in ["analyze", "trading", "market"])
            },
            {
                "message": "Analyze AAPL for trading",
                "expect_type": "guru_analysis",
                "check": lambda r: any(phrase in r.lower() for phrase in ["why this trade", "confidence", "$"])
            }
        ]
        
        passed = 0
        for i, test in enumerate(conversation_tests, 1):
            try:
                response = requests.post(
                    f"{self.base_url}/api/v1/chat",
                    json={
                        "message": test["message"],
                        "session_id": f"{self.session_id}-conv",
                        "user_id": "web_user",
                        "context": {"interface": "web", "timestamp": datetime.now().isoformat()}
                    },
                    timeout=20
                )
                
                if response.status_code == 200:
                    data = response.json()
                    response_text = data.get('response', '')
                    response_type = data.get('type', 'unknown')
                    
                    meets_expectation = test["check"](response_text)
                    is_appropriate_length = len(response_text) > 20
                    
                    success = meets_expectation and is_appropriate_length
                    details = f"Type: {response_type}, Length: {len(response_text)}, Expectation: {meets_expectation}"
                    
                    if self.log_result(f"Conversation {i}: {test['expect_type']}", success, details, response_text):
                        passed += 1
                else:
                    self.log_result(f"Conversation {i}: {test['expect_type']}", False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"Conversation {i}: {test['expect_type']}", False, f"Error: {e}")
        
        return passed, len(conversation_tests)

    def test_market_data_display(self):
        """Test market data endpoints for web interface display"""
        print("\n[DATA] TESTING MARKET DATA FOR WEB DISPLAY")
        print("=" * 50)
        
        symbols = ["AAPL", "MSFT", "GOOGL", "TSLA"]
        passed = 0
        
        for symbol in symbols:
            try:
                response = requests.get(f"{self.base_url}/api/v1/quote/{symbol}", timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Check for required fields for web display
                    required_fields = ['symbol', 'price', 'change']
                    has_required = all(field in data for field in required_fields)
                    
                    price = data.get('price', 0)
                    is_valid_price = isinstance(price, (int, float)) and price > 0
                    
                    success = has_required and is_valid_price
                    details = f"Price: ${price}, Fields: {list(data.keys())}"
                    
                    if self.log_result(f"Market Data: {symbol}", success, details):
                        passed += 1
                else:
                    self.log_result(f"Market Data: {symbol}", False, f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_result(f"Market Data: {symbol}", False, f"Error: {e}")
        
        return passed, len(symbols)

    def test_web_interface_compatibility(self):
        """Test web interface specific features"""
        print("\n[WEB] TESTING WEB INTERFACE COMPATIBILITY")
        print("=" * 50)
        
        # Test CORS headers
        try:
            response = requests.options(f"{self.base_url}/api/v1/chat", timeout=5)
            cors_enabled = response.status_code in [200, 204]
            self.log_result("CORS Support", cors_enabled, f"OPTIONS response: {response.status_code}")
        except Exception as e:
            self.log_result("CORS Support", False, f"Error: {e}")
        
        # Test JSON response format
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/chat",
                json={"message": "test", "session_id": "test"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                has_response = 'response' in data
                has_type = 'type' in data
                has_confidence = 'confidence' in data
                
                json_format_ok = has_response and has_type
                details = f"Response: {has_response}, Type: {has_type}, Confidence: {has_confidence}"
                
                self.log_result("JSON Response Format", json_format_ok, details)
            else:
                self.log_result("JSON Response Format", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_result("JSON Response Format", False, f"Error: {e}")

    def check_html_interface_file(self):
        """Check if HTML interface file exists and has correct structure"""
        print("\n[DOC] CHECKING HTML INTERFACE FILE")
        print("=" * 50)
        
        html_path = "atlas_interface.html"
        
        if os.path.exists(html_path):
            self.log_result("HTML File Exists", True, f"Found: {html_path}")
            
            try:
                with open(html_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for key elements
                has_chat_api = '/api/v1/chat' in content
                has_session_id = 'session_id' in content
                has_dual_panels = 'left-panel' in content and 'right-panel' in content
                has_send_message = 'sendMessage' in content
                
                structure_ok = has_chat_api and has_session_id and has_dual_panels and has_send_message
                details = f"API: {has_chat_api}, Session: {has_session_id}, Panels: {has_dual_panels}, Send: {has_send_message}"
                
                self.log_result("HTML Structure", structure_ok, details)
                
            except Exception as e:
                self.log_result("HTML Structure", False, f"Error reading file: {e}")
        else:
            self.log_result("HTML File Exists", False, f"Not found: {html_path}")

    def run_web_interface_validation(self):
        """Run complete web interface validation"""
        print("[WEB] A.T.L.A.S. WEB INTERFACE INTEGRATION TEST")
        print("=" * 60)
        print("Testing frontend-backend communication and compatibility")
        
        # Check HTML file first
        self.check_html_interface_file()
        
        # Test API endpoints
        api_passed, api_total = self.test_api_endpoints()
        
        # Test conversational flow
        conv_passed, conv_total = self.test_conversational_flow_via_api()
        
        # Test market data
        market_passed, market_total = self.test_market_data_display()
        
        # Test web compatibility
        self.test_web_interface_compatibility()
        
        # Calculate results
        total_passed = api_passed + conv_passed + market_passed
        total_tests = api_total + conv_total + market_total
        pass_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # Generate report
        print("\n" + "=" * 60)
        print("📋 WEB INTERFACE INTEGRATION REPORT")
        print("=" * 60)
        
        print(f"[DATA] Overall Results:")
        print(f"   • Total Tests: {total_tests}")
        print(f"   • Passed: {total_passed}")
        print(f"   • Failed: {total_tests - total_passed}")
        print(f"   • Pass Rate: {pass_rate:.1f}%")
        
        print(f"\n[UP] Category Breakdown:")
        print(f"   • API Endpoints: {api_passed}/{api_total} ({api_passed/api_total*100:.0f}%)")
        print(f"   • Conversational Flow: {conv_passed}/{conv_total} ({conv_passed/conv_total*100:.0f}%)")
        print(f"   • Market Data: {market_passed}/{market_total} ({market_passed/market_total*100:.0f}%)")
        
        if pass_rate >= 90:
            print("\n[SUCCESS] WEB INTERFACE READY!")
            print("   [OK] Frontend-backend communication working perfectly")
            print("   [OK] All API endpoints responding correctly")
            print("   [OK] Conversational AI integrated with web interface")
            print("   [OK] Market data flowing to frontend")
        elif pass_rate >= 75:
            print("\n[OK] WEB INTERFACE MOSTLY FUNCTIONAL")
            print("   [OK] Core functionality working")
            print("   [WARN] Minor issues present")
        else:
            print("\n[WARN] WEB INTERFACE NEEDS FIXES")
            print("   [ERROR] Significant issues detected")
        
        # Save results
        with open('web_interface_test_results.json', 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'pass_rate': pass_rate,
                'total_passed': total_passed,
                'total_tests': total_tests,
                'detailed_results': self.results
            }, f, indent=2)
        
        print(f"\n[DOC] Detailed results saved to: web_interface_test_results.json")
        
        return pass_rate >= 75

if __name__ == "__main__":
    validator = WebInterfaceValidator()
    is_ready = validator.run_web_interface_validation()
    
    if is_ready:
        print("\n[TARGET] Web interface is ready for production use!")
    else:
        print("\n[TOOL] Web interface needs additional fixes.")
