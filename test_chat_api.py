#!/usr/bin/env python3
"""
Test the A.T.L.A.S. chat API to verify conversational AI functionality
"""
import requests
import json
import time

def test_chat_api():
    """Test the conversational AI interface"""
    
    # Wait for server to be ready
    print("🔄 Waiting for A.T.L.A.S. to be ready...")
    time.sleep(5)
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_messages = [
        "Hello, I'm interested in trading AAPL. Can you analyze it for me?",
        "What's the current market sentiment for tech stocks?",
        "I want to make $200 today, what are my best options?",
        "Can you scan for TTM Squeeze signals?",
        "Explain options trading to me like I'm a beginner"
    ]
    
    print("[LAUNCH] Testing A.T.L.A.S. Conversational AI Interface")
    print("=" * 60)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n[NOTE] Test {i}: {message}")
        print("-" * 40)
        
        payload = {
            "message": message,
            "session_id": f"test_session_{i}"
        }
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get("response", "")
                response_type = data.get("type", "unknown")
                confidence = data.get("confidence", 0.0)
                
                print(f"[OK] Status: {response.status_code}")
                print(f"[DATA] Type: {response_type}")
                print(f"[TARGET] Confidence: {confidence:.2f}")
                print(f"📏 Length: {len(response_text)} characters")
                
                # Show first 200 characters of response
                preview = response_text[:200] + "..." if len(response_text) > 200 else response_text
                print(f"💬 Response Preview: {preview}")
                
                # Check for key indicators
                if "A.T.L.A.S" in response_text:
                    print("[OK] A.T.L.A.S. branding detected")
                if "Predicto" in response_text:
                    print("[OK] Predicto interface detected")
                if any(word in response_text.lower() for word in ["trade", "stock", "analysis", "market"]):
                    print("[OK] Trading content detected")
                    
            else:
                print(f"[ERROR] Request failed with status {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"[ERROR] Error: {e}")
        
        print()
    
    print("[FINISH] Chat API testing completed!")

if __name__ == "__main__":
    test_chat_api()
